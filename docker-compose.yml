version: '3.8'

services:
  # Ollama LLM Service
  ollama:
    image: ollama/ollama:latest
    container_name: expedition-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - expedition-network

  # Expedition Planner Application
  expedition-planner:
    build:
      context: .
      target: production
    container_name: expedition-planner-app
    ports:
      - "8080:8080"
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - OLLAMA_MODEL=mistral-7b-v0-1-gguf:latest
      - WEB_HOST=0.0.0.0
      - WEB_PORT=8080
      - LOG_LEVEL=INFO
      - SECRET_KEY=${SECRET_KEY:-change-this-in-production}
      - MAX_FILE_SIZE_MB=50
      - C<PERSON>ANUP_DAYS=7
    volumes:
      - expedition_uploads:/app/expedition_planner/uploads
      - expedition_outputs:/app/consolidated_outputs
      - expedition_logs:/app/logs
    depends_on:
      ollama:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/validate-configuration"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - expedition-network

  # Redis for session management (optional)
  redis:
    image: redis:7-alpine
    container_name: expedition-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - expedition-network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: expedition-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deployment/ssl:/etc/nginx/ssl:ro
      - expedition_logs:/var/log/nginx
    depends_on:
      - expedition-planner
    restart: unless-stopped
    networks:
      - expedition-network

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: expedition-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - expedition-network

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: expedition-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./deployment/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - expedition-network

volumes:
  ollama_data:
    driver: local
  expedition_uploads:
    driver: local
  expedition_outputs:
    driver: local
  expedition_logs:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  expedition-network:
    driver: bridge
