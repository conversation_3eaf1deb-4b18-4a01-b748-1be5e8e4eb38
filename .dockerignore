# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
expedition_planner/uploads/*
consolidated_outputs/*
logs/*
temp_processing/*
*.egg-info/
dist/
build/

# Documentation
docs/_build/
*.md
!README.md

# Test files
tests/
*.test
test_*

# Development files
.pytest_cache/
.coverage
htmlcov/
.tox/

# Deployment files (keep only what's needed)
deployment/ssl/private/*
*.key
*.pem

# Large files
*.zip
*.tar.gz
*.pdf
*.docx
*.xlsx

# Temporary files
tmp/
temp/
*.tmp
*.temp
