# Expedition Planner Environment Configuration
# Copy this file to .env and customize for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Web Server Configuration
WEB_HOST=0.0.0.0
WEB_PORT=8080
WEB_DEBUG=false
SECRET_KEY=your-secret-key-change-this-in-production

# =============================================================================
# OLLAMA LLM CONFIGURATION
# =============================================================================

# Ollama Service URL
OLLAMA_BASE_URL=http://localhost:11434

# Model to use (must be available in Ollama)
OLLAMA_MODEL=mistral-7b-v0-1-gguf:latest

# LLM Parameters
OLLAMA_TEMPERATURE=0.1
OLLAMA_TIMEOUT=60
OLLAMA_NUM_CTX=4096
OLLAMA_NUM_PREDICT=1024

# =============================================================================
# FILE PROCESSING CONFIGURATION
# =============================================================================

# File size limits (in MB)
MAX_FILE_SIZE_MB=50
MAX_CONTENT_LENGTH=104857600

# File cleanup settings
CLEANUP_DAYS=7
MAX_STORAGE_MB=100

# Supported file extensions (comma-separated)
SUPPORTED_EXTENSIONS=.pdf,.docx,.doc,.txt,.md

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Parallel processing
MAX_WORKERS=4
PARALLEL_CHUNK_SIZE=2

# Memory management
MAX_MEMORY_PERCENT=80.0
MAX_MEMORY_MB=500.0

# LLM optimization
LLM_CONTEXT_LIMIT=4096
CACHE_SIZE_LIMIT=100

# =============================================================================
# CIRCUIT BREAKER CONFIGURATION
# =============================================================================

# LLM circuit breaker
LLM_FAILURE_THRESHOLD=3
LLM_RECOVERY_TIMEOUT=30

# File processing circuit breaker
FILE_FAILURE_THRESHOLD=5
FILE_RECOVERY_TIMEOUT=60

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file path (optional, logs to console if not set)
LOG_FILE=/app/logs/expedition_planner.log

# Log file rotation
LOG_MAX_FILE_SIZE_MB=10
LOG_BACKUP_COUNT=5

# =============================================================================
# DATABASE CONFIGURATION (Optional - for future use)
# =============================================================================

# Redis for session management
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# PostgreSQL for data persistence (future feature)
DATABASE_URL=postgresql://user:password@localhost:5432/expedition_planner

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================

# Prometheus metrics
ENABLE_METRICS=true
METRICS_PORT=9091

# Health check endpoints
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# CORS settings
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# File upload security
ALLOWED_MIME_TYPES=application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================

# SSL settings (for production)
SSL_ENABLED=false
SSL_CERT_PATH=/etc/ssl/certs/expedition_planner.crt
SSL_KEY_PATH=/etc/ssl/private/expedition_planner.key

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Backup settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=/backups

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development mode settings (only for development)
DEV_MODE=false
DEV_AUTO_RELOAD=false
DEV_PROFILING=false

# Testing settings
TEST_MODE=false
TEST_DATABASE_URL=sqlite:///test.db

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Email notifications (optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true

# Webhook notifications (optional)
WEBHOOK_URL=
WEBHOOK_SECRET=

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
ENABLE_PATTERN_ANALYSIS=true
ENABLE_BATCH_PROCESSING=true
ENABLE_API_DOCS=true
ENABLE_ADMIN_INTERFACE=false

# =============================================================================
# DEPLOYMENT SPECIFIC
# =============================================================================

# Environment identifier
ENVIRONMENT=production
DEPLOYMENT_VERSION=2.0.0

# Container settings
CONTAINER_NAME=expedition-planner
RESTART_POLICY=unless-stopped

# Resource limits
MEMORY_LIMIT=2g
CPU_LIMIT=2.0
