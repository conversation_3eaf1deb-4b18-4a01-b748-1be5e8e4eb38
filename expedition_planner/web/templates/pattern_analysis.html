<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyze Operation Patterns - Expedition Report Converter</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .pattern-card {
            border-left: 4px solid #1a3a8f;
            margin-bottom: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .pattern-insight {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 0.5rem 0;
        }
        .duration-pattern {
            border-left-color: #28a745;
        }
        .location-pattern {
            border-left-color: #ffc107;
        }
        .operational-pattern {
            border-left-color: #dc3545;
        }
        .environmental-pattern {
            border-left-color: #17a2b8;
        }
        .analysis-section {
            margin-bottom: 2rem;
        }
        .pattern-metric {
            text-align: center;
            padding: 1rem;
            background: linear-gradient(135deg, #1a3a8f 0%, #0f2557 100%);
            color: white;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        .pattern-chart {
            height: 300px;
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
        .file-selection-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 0.5rem;
        }
        .file-item {
            padding: 0.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .file-checkbox {
            margin-right: 1rem;
        }
        .file-info {
            flex-grow: 1;
        }
        .file-name {
            font-weight: 500;
            display: block;
        }
        .file-meta {
            font-size: 0.75rem;
            color: #6c757d;
        }
        .location-filter {
            margin-bottom: 1rem;
        }
        .filter-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            margin: 0.25rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .filter-badge.active {
            background: #1a3a8f;
            color: white;
            border-color: #1a3a8f;
        }
        .insight-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background: #e9ecef;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .insight-tag.tide { background-color: #d1ecf1; color: #0c5460; }
        .insight-tag.weather { background-color: #fff3cd; color: #856404; }
        .insight-tag.resource { background-color: #d4edda; color: #155724; }
        .insight-tag.activity { background-color: #f8d7da; color: #721c24; }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
        }
        .comparison-table th, .comparison-table td {
            padding: 0.5rem;
            border: 1px solid #dee2e6;
        }
        .comparison-table th {
            background-color: #f8f9fa;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .pattern-visualization {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-file-export me-2"></i>
                Expedition Report Converter
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/agent-interface">Convert Documents</a>
                <a class="nav-link active" href="/pattern-analysis">Analyze Patterns</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-chart-line me-2"></i> Operation Pattern Analysis
                </h1>
                <p class="lead">Discover operational patterns by analyzing multiple expedition JSON templates together.</p>

                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>How Pattern Analysis Works</h6>
                    <p class="mb-2">
                        Pattern analysis becomes meaningful when comparing <strong>multiple operations</strong>.
                        Select JSON files from different expedition days to discover:
                    </p>
                    <ul class="mb-0">
                        <li><strong>Timing Patterns:</strong> Why some operations are AM-only vs PM-only vs full-day</li>
                        <li><strong>Location Insights:</strong> How location characteristics affect operational decisions</li>
                        <li><strong>Duration Logic:</strong> What determines 2-hour vs 4-hour vs full-day operations</li>
                        <li><strong>Environmental Factors:</strong> How tides, weather, and daylight influence scheduling</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- JSON File Selection Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-code me-2"></i>
                            Select Multiple JSON Files for Comparative Analysis
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="location-filter mb-3">
                            <h6 class="mb-2">Filter by Location:</h6>
                            <div>
                                <span class="filter-badge active" data-location="all">All Locations</span>
                                <span class="filter-badge" data-location="montgomery-reef">Montgomery Reef</span>
                                <span class="filter-badge" data-location="king-george-river">King George River</span>
                                <span class="filter-badge" data-location="the-lacepedes">The Lacepedes</span>
                                <span class="filter-badge" data-location="other">Other Locations</span>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="file-selection-list">
                                    <div id="json-file-list">
                                        <!-- File items will be loaded here -->
                                        <div class="text-center py-3">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">Loading available JSON files...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="mb-3">Selection Summary</h6>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Selected Files:</span>
                                            <span id="selected-count" class="badge bg-primary">0</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Locations:</span>
                                            <span id="selected-locations" class="badge bg-secondary">0</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-3">
                                            <span>Operation Types:</span>
                                            <span id="selected-types" class="badge bg-secondary">0</span>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button id="refresh-files-btn" class="btn btn-outline-secondary">
                                                <i class="fas fa-sync-alt me-2"></i>
                                                Refresh Files
                                            </button>
                                            <button id="analyze-selected-btn" class="btn btn-primary" disabled>
                                                <i class="fas fa-chart-line me-2"></i>
                                                Analyze Selected Files
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Controls -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i> Analysis Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="analysisForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="analysisType" class="form-label">Analysis Focus</label>
                                    <select class="form-select" id="analysisType">
                                        <option value="comprehensive">Comprehensive Analysis</option>
                                        <option value="duration">Duration Patterns</option>
                                        <option value="location">Location-specific Patterns</option>
                                        <option value="environmental">Environmental Dependencies</option>
                                        <option value="operational">Operational Efficiency</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="visualizationType" class="form-label">Visualization Type</label>
                                    <select class="form-select" id="visualizationType">
                                        <option value="charts">Interactive Charts</option>
                                        <option value="tables">Comparison Tables</option>
                                        <option value="insights">Text Insights</option>
                                        <option value="all">All Visualizations</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-primary" id="runAnalysisBtn">
                                    <i class="fas fa-play me-2"></i> Run Analysis
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="exportAnalysisBtn" disabled>
                                    <i class="fas fa-download me-2"></i> Export Results
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="row">
                    <div class="col-6">
                        <div class="pattern-metric">
                            <h3 id="totalFiles">0</h3>
                            <p class="mb-0">JSON Files</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="pattern-metric">
                            <h3 id="totalLocations">0</h3>
                            <p class="mb-0">Locations</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Results -->
        <div id="analysisResults" style="display: none;">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i> Analysis Summary
                            </h5>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary" id="toggle-view-btn">
                                    <i class="fas fa-th-large me-1"></i>
                                    Toggle View
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="pattern-visualization">
                                <div id="summary-chart" style="height: 300px;"></div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">Key Insights</h6>
                                            <div id="key-insights">
                                                <div class="insight-item">
                                                    <p><strong>Tide-dependent operations</strong> are scheduled primarily in the morning at Montgomery Reef</p>
                                                    <div>
                                                        <span class="insight-tag tide">Tide</span>
                                                        <span class="insight-tag activity">AM Operations</span>
                                                    </div>
                                                </div>
                                                <hr>
                                                <div class="insight-item">
                                                    <p><strong>Full-day operations</strong> are more common at locations with multiple activity options</p>
                                                    <div>
                                                        <span class="insight-tag activity">Activities</span>
                                                        <span class="insight-tag resource">Duration</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">Operation Type Distribution</h6>
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Operation Type</th>
                                                        <th>Count</th>
                                                        <th>Percentage</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>AM Only</td>
                                                        <td>12</td>
                                                        <td>40%</td>
                                                    </tr>
                                                    <tr>
                                                        <td>PM Only</td>
                                                        <td>8</td>
                                                        <td>27%</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Full Day</td>
                                                        <td>10</td>
                                                        <td>33%</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Duration Patterns -->
            <div class="analysis-section">
                <div class="card pattern-card duration-pattern">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i> Duration Patterns
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div id="durationInsights">
                                    <div class="pattern-insight">
                                        <h6>Short Operations (2-3 Hours)</h6>
                                        <p class="mb-1">Short duration operations typically occur when:</p>
                                        <ul class="mb-0">
                                            <li>Tide conditions limit access time (Montgomery Reef)</li>
                                            <li>Weather windows are narrow (exposed locations)</li>
                                            <li>Location has limited activities (single-feature sites)</li>
                                        </ul>
                                    </div>
                                    <div class="pattern-insight">
                                        <h6>Full-Day Operations (6+ Hours)</h6>
                                        <p class="mb-1">Extended operations are planned for:</p>
                                        <ul class="mb-0">
                                            <li>Complex multi-activity sites (King George River)</li>
                                            <li>Favorable weather conditions (protected locations)</li>
                                            <li>High guest interest locations (signature experiences)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="pattern-chart">
                                    <div id="duration-chart" style="width: 100%; height: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Patterns -->
            <div class="analysis-section">
                <div class="card pattern-card location-pattern">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i> Location Patterns
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div id="locationInsights">
                                    <div class="pattern-insight">
                                        <h6>Montgomery Reef</h6>
                                        <p class="mb-1">Operations are strongly tide-dependent:</p>
                                        <ul class="mb-0">
                                            <li>AM operations coincide with falling tide (reef exposure)</li>
                                            <li>Zodiac tours are the primary activity</li>
                                            <li>Operations rarely exceed 4 hours</li>
                                        </ul>
                                    </div>
                                    <div class="pattern-insight">
                                        <h6>King George River</h6>
                                        <p class="mb-1">Operations feature multiple activity options:</p>
                                        <ul class="mb-0">
                                            <li>Full-day operations are common</li>
                                            <li>Combination of zodiac tours and hiking activities</li>
                                            <li>Less tide-dependent than coastal reef locations</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="pattern-chart">
                                    <div id="location-chart" style="width: 100%; height: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Environmental Patterns -->
            <div class="analysis-section">
                <div class="card pattern-card environmental-pattern">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-water me-2"></i> Environmental Dependencies
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div id="environmentalInsights">
                                    <div class="pattern-insight">
                                        <h6>Tide-Dependent Operations</h6>
                                        <p class="mb-1">Reef and coastal operations show strong tide correlations:</p>
                                        <ul class="mb-0">
                                            <li>Low tide operations focus on exposed reef features</li>
                                            <li>High tide operations enable better zodiac access to certain areas</li>
                                            <li>Tide timing directly influences operation scheduling</li>
                                        </ul>
                                    </div>
                                    <div class="pattern-insight">
                                        <h6>Weather Considerations</h6>
                                        <p class="mb-1">Weather patterns affect operation planning:</p>
                                        <ul class="mb-0">
                                            <li>Morning operations often have calmer conditions</li>
                                            <li>Exposed locations are scheduled during optimal weather windows</li>
                                            <li>Seasonal patterns influence location selection</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="pattern-chart">
                                    <div id="environmental-chart" style="width: 100%; height: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Operational Patterns -->
            <div class="analysis-section">
                <div class="card pattern-card operational-pattern">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i> Operational Efficiency
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div id="operationalInsights">
                                    <div class="pattern-insight">
                                        <h6>Resource Allocation</h6>
                                        <p class="mb-1">Zodiac and guide distribution patterns:</p>
                                        <ul class="mb-0">
                                            <li>Larger groups require more zodiacs and guides</li>
                                            <li>Complex locations require higher staff-to-guest ratios</li>
                                            <li>Equipment needs vary significantly by location</li>
                                        </ul>
                                    </div>
                                    <div class="pattern-insight">
                                        <h6>Timing Optimization</h6>
                                        <p class="mb-1">Scheduling patterns for different operation types:</p>
                                        <ul class="mb-0">
                                            <li>AM operations typically start between 07:00-09:00</li>
                                            <li>PM operations typically start between 13:00-15:00</li>
                                            <li>Full-day operations maximize daylight hours</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="pattern-chart">
                                    <div id="operational-chart" style="width: 100%; height: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div id="analysisLoading" style="display: none;">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Running pattern analysis...</p>
                <div class="progress mt-3" style="height: 10px;">
                    <div id="analysis-progress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- No Data State -->
        <div id="noDataState">
            <div class="text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Analysis Data Available</h4>
                <p class="text-muted">Process some expedition documents first to generate pattern analysis.</p>
                <a href="/agent-interface" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i> Convert Documents
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">
                &copy; 2024 Expedition Report Converter. 
                Powered by Advanced Document Processing Technology.
            </p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    class PatternAnalysis {
        constructor() {
            this.selectedFiles = new Set();
            this.initializeElements();
            this.bindEvents();
            this.loadJsonFiles();
            this.setupCharts();
        }

        initializeElements() {
            this.elements = {
                analysisType: document.getElementById('analysisType'),
                visualizationType: document.getElementById('visualizationType'),
                runAnalysisBtn: document.getElementById('runAnalysisBtn'),
                exportAnalysisBtn: document.getElementById('exportAnalysisBtn'),
            analysisResults: document.getElementById('analysisResults'),
            analysisLoading: document.getElementById('analysisLoading'),
            noDataState: document.getElementById('noDataState'),
            totalSessions: document.getElementById('totalSessions'),
            // New JSON file selection elements
            jsonFileList: document.getElementById('json-file-list'),
            refreshFilesBtn: document.getElementById('refresh-files-btn'),
            analyzeSelectedBtn: document.getElementById('analyze-selected-btn'),
            selectedCount: document.getElementById('selected-count')
        };
    }

    bindEvents() {
        this.elements.runAnalysisBtn.addEventListener('click', () => this.runAnalysis());
        this.elements.exportAnalysisBtn.addEventListener('click', () => this.exportResults());
        this.elements.sessionSelect.addEventListener('change', () => this.onSessionChange());
        // New JSON file selection events
        this.elements.refreshFilesBtn.addEventListener('click', () => this.loadJsonFiles());
        this.elements.analyzeSelectedBtn.addEventListener('click', () => this.analyzeSelectedFiles());
    }

    async loadSessions() {
        try {
            // This would load actual session data from the backend
            // For now, show placeholder
            this.elements.totalSessions.textContent = '0';
            this.elements.noDataState.style.display = 'block';
        } catch (error) {
            console.error('Error loading sessions:', error);
        }
    }

    onSessionChange() {
        const hasSession = this.elements.sessionSelect.value !== '';
        this.elements.runAnalysisBtn.disabled = !hasSession;
    }

    async runAnalysis() {
        const sessionId = this.elements.sessionSelect.value;
        const analysisType = this.elements.analysisType.value;

        if (!sessionId) {
            alert('Please select a processing session first.');
            return;
        }

        try {
            this.showLoading();
            
            // This would call the actual analysis endpoint
            // For now, show mock results
            setTimeout(() => {
                this.showResults();
            }, 2000);

        } catch (error) {
            console.error('Error running analysis:', error);
            alert('Error running analysis: ' + error.message);
            this.hideLoading();
        }
    }

    showLoading() {
        this.elements.analysisLoading.style.display = 'block';
        this.elements.analysisResults.style.display = 'none';
        this.elements.noDataState.style.display = 'none';
        this.elements.runAnalysisBtn.disabled = true;
    }

    hideLoading() {
        this.elements.analysisLoading.style.display = 'none';
        this.elements.runAnalysisBtn.disabled = false;
    }

    showResults() {
        this.hideLoading();
        this.elements.analysisResults.style.display = 'block';
        this.elements.noDataState.style.display = 'none';
        this.elements.exportAnalysisBtn.disabled = false;
    }

    async exportResults() {
        try {
            // This would export the analysis results
            alert('Export functionality would be implemented here.');
        } catch (error) {
            console.error('Error exporting results:', error);
            alert('Error exporting results: ' + error.message);
        }
    }

    async loadJsonFiles() {
        try {
            const response = await fetch('/api/list-json-files');
            const data = await response.json();

            if (data.success) {
                this.renderJsonFileList(data.json_files);
            } else {
                this.elements.jsonFileList.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading JSON files: ${data.error}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading JSON files:', error);
            this.elements.jsonFileList.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Failed to load JSON files: ${error.message}
                </div>
            `;
        }
    }

    renderJsonFileList(files) {
        if (files.length === 0) {
            this.elements.jsonFileList.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-file-code fa-3x mb-3"></i>
                    <p>No JSON files found. Process some expeditions first to generate JSON templates.</p>
                </div>
            `;
            return;
        }

        const fileListHtml = files.map(file => `
            <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" value="${file.path}"
                       id="file-${file.path.replace(/[^a-zA-Z0-9]/g, '_')}"
                       onchange="patternAnalysis.onFileSelectionChange()">
                <label class="form-check-label" for="file-${file.path.replace(/[^a-zA-Z0-9]/g, '_')}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${file.name}</strong>
                            <br>
                            <small class="text-muted">
                                <i class="fas fa-folder me-1"></i>${file.expedition}
                                <i class="fas fa-clock ms-2 me-1"></i>${new Date(file.modified).toLocaleString()}
                            </small>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${(file.size / 1024).toFixed(1)} KB</small>
                        </div>
                    </div>
                </label>
            </div>
        `).join('');

        this.elements.jsonFileList.innerHTML = `
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">Available JSON Files (${files.length})</h6>
                    <div>
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="patternAnalysis.selectAllFiles()">
                            Select All
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="patternAnalysis.clearSelection()">
                            Clear All
                        </button>
                    </div>
                </div>
                <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                    ${fileListHtml}
                </div>
            </div>
        `;
    }

    onFileSelectionChange() {
        const checkboxes = this.elements.jsonFileList.querySelectorAll('input[type="checkbox"]');
        this.selectedFiles.clear();

        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                this.selectedFiles.add(checkbox.value);
            }
        });

        this.elements.selectedCount.textContent = this.selectedFiles.size;
        this.elements.analyzeSelectedBtn.disabled = this.selectedFiles.size === 0;
    }

    selectAllFiles() {
        const checkboxes = this.elements.jsonFileList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        this.onFileSelectionChange();
    }

    clearSelection() {
        const checkboxes = this.elements.jsonFileList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        this.onFileSelectionChange();
    }

    async analyzeSelectedFiles() {
        if (this.selectedFiles.size === 0) {
            alert('Please select at least one JSON file to analyze.');
            return;
        }

        try {
            this.elements.analyzeSelectedBtn.disabled = true;
            this.elements.analyzeSelectedBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';

            const response = await fetch('/api/analyze-json-files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    json_files: Array.from(this.selectedFiles)
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayAnalysisResults(data);
            } else {
                alert('Error analyzing files: ' + data.error);
            }
        } catch (error) {
            console.error('Error analyzing selected files:', error);
            alert('Error analyzing files: ' + error.message);
        } finally {
            this.elements.analyzeSelectedBtn.disabled = false;
            this.elements.analyzeSelectedBtn.innerHTML = '<i class="fas fa-chart-line me-2"></i>Analyze Selected Files';
        }
    }

    displayAnalysisResults(data) {
        const resultsHtml = `
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>Analysis Complete</h5>
                <p class="mb-0">
                    Analyzed ${data.files_analyzed} files containing ${data.days_analyzed} expedition days.
                </p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Pattern Analysis Report</h5>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded">${data.pattern_report}</pre>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">Detailed Analysis Results</h5>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded">${JSON.stringify(data.analysis_result, null, 2)}</pre>
                </div>
            </div>
        `;

        this.elements.analysisResults.innerHTML = resultsHtml;
        this.elements.analysisResults.style.display = 'block';

        // Scroll to results
        this.elements.analysisResults.scrollIntoView({ behavior: 'smooth' });
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    new PatternAnalysis();
});
</script>
</body>
</html>
