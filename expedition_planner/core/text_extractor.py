"""
Simple text extractor to replace Docling dependency.
Handles PDF, DOCX, and image files with clean raw text output.
"""

import logging
import mimetypes
from pathlib import Path
from typing import List, Optional

from ..utils.performance import performance_monitor, memory_efficient, llm_optimizer
from ..utils.error_handlers import file_processing_error_handler

# Optional imports for document processing
try:
    import fitz  # PyMuPDF

    HAS_PYMUPDF = True
except ImportError:
    fitz = None
    HAS_PYMUPDF = False

try:
    from docx import Document

    HAS_PYTHON_DOCX = True
except ImportError:
    Document = None
    HAS_PYTHON_DOCX = False

try:
    import pytesseract
    from PIL import Image

    HAS_OCR = True
except ImportError:
    pytesseract = None
    Image = None
    HAS_OCR = False

logger = logging.getLogger(__name__)


class TextExtractor:
    """Simple text extractor for expedition documents."""

    def __init__(self):
        """Initialize the text extractor."""
        self.supported_formats = {
            ".pdf": self._extract_from_pdf,
            ".docx": self._extract_from_docx,
            ".doc": self._extract_from_docx,
            ".txt": self._extract_from_txt,
            ".png": self._extract_from_image,
            ".jpg": self._extract_from_image,
            ".jpeg": self._extract_from_image,
            ".tiff": self._extract_from_image,
            ".bmp": self._extract_from_image,
        }

        # Configuration
        self.max_file_size = 50 * 1024 * 1024  # 50MB

    @performance_monitor
    @memory_efficient(max_memory_mb=200.0)
    @file_processing_error_handler(default_return=None)
    def extract_text(self, file_path: str) -> Optional[str]:
        """
        Extract text from a document file.

        Args:
            file_path: Path to the document file

        Returns:
            Extracted text content or None if extraction fails
        """
        try:
            path = Path(file_path)

            # Validate file
            if not self._validate_file(path):
                return None

            # Get file extension
            file_extension = path.suffix.lower()

            # Check if format is supported
            if file_extension not in self.supported_formats:
                logger.error(f"Unsupported file format: {file_extension}")
                return None

            # Extract text using appropriate method
            extractor_method = self.supported_formats[file_extension]
            text = extractor_method(path)

            if text:
                logger.info(f"Successfully extracted text from: {file_path}")
                return self._clean_text(text)
            else:
                logger.warning(f"No text extracted from: {file_path}")
                return None

        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            return None

    def extract_from_multiple_files(self, file_paths: List[str]) -> List[str]:
        """
        Extract text from multiple files.

        Args:
            file_paths: List of file paths to process

        Returns:
            List of extracted text content (in same order as input files)
        """
        results = []

        for file_path in file_paths:
            text = self.extract_text(file_path)
            if text:
                results.append(text)
            else:
                logger.warning(f"Failed to extract text from: {file_path}")
                results.append("")  # Add empty string to maintain order

        return results

    def _validate_file(self, path: Path) -> bool:
        """Validate if a file can be processed."""
        try:
            # Check if file exists
            if not path.exists():
                logger.error(f"File not found: {path}")
                return False

            # Check file size
            file_size = path.stat().st_size
            if file_size > self.max_file_size:
                logger.error(
                    f"File too large: {file_size} bytes (max: {self.max_file_size})"
                )
                return False

            # Check if file is readable
            if not path.is_file():
                logger.error(f"Path is not a file: {path}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating file {path}: {e}")
            return False

    def _extract_from_pdf(self, path: Path) -> Optional[str]:
        """Extract text from PDF using PyMuPDF."""
        if not HAS_PYMUPDF:
            logger.error(
                "PyMuPDF (fitz) not installed. Install with: pip install PyMuPDF"
            )
            return None

        try:
            doc = fitz.open(str(path))
            text_content = []

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                if text.strip():
                    text_content.append(text)

            doc.close()

            if text_content:
                return "\n\n".join(text_content)
            else:
                logger.warning(f"No text found in PDF: {path}")
                return None

        except Exception as e:
            logger.error(f"Error extracting from PDF {path}: {e}")
            return None

    def _extract_from_docx(self, path: Path) -> Optional[str]:
        """Extract text from DOCX using python-docx."""
        if not HAS_PYTHON_DOCX:
            logger.error(
                "python-docx not installed. Install with: pip install python-docx"
            )
            return None

        try:
            doc = Document(str(path))
            text_content = []

            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))

            if text_content:
                return "\n".join(text_content)
            else:
                logger.warning(f"No text found in DOCX: {path}")
                return None

        except Exception as e:
            logger.error(f"Error extracting from DOCX {path}: {e}")
            return None

    def _extract_from_txt(self, path: Path) -> Optional[str]:
        """Extract text from plain text file."""
        try:
            with open(path, encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(path, encoding="latin-1") as f:
                    return f.read()
            except Exception as e:
                logger.error(f"Error reading text file {path}: {e}")
                return None
        except Exception as e:
            logger.error(f"Error extracting from text file {path}: {e}")
            return None

    def _extract_from_image(self, path: Path) -> Optional[str]:
        """Extract text from image using OCR (pytesseract)."""
        if not HAS_OCR:
            logger.error(
                "pytesseract or PIL not installed. Install with: pip install pytesseract pillow"
            )
            logger.error("Also ensure tesseract is installed on your system")
            return None

        try:
            # Open image
            image = Image.open(str(path))

            # Extract text using OCR
            text = pytesseract.image_to_string(image)

            if text.strip():
                return text
            else:
                logger.warning(f"No text found in image: {path}")
                return None

        except Exception as e:
            logger.error(f"Error extracting from image {path}: {e}")
            return None

    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        if not text:
            return ""

        import re

        # Remove hyphenated line breaks (e.g., "King-\nGeorge" -> "KingGeorge")
        text = re.sub(r'(\w+)-\n(\w+)', r'\1\2', text)

        # Normalize line breaks and strip whitespace
        lines = text.splitlines()
        cleaned_lines = [line.strip() for line in lines if line.strip()]

        # Collapse multiple spaces to single
        cleaned_text = "\n".join(cleaned_lines)
        cleaned_text = re.sub(r" +", " ", cleaned_text)

        return cleaned_text


def create_text_extractor() -> TextExtractor:
    """Create a text extractor instance."""
    return TextExtractor()
