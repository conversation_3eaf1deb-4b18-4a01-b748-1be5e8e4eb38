"""
Development-specific configuration overrides.
"""

import os
from typing import Any, Dict

def get_dev_config_overrides() -> Dict[str, Any]:
    """
    Get development-specific configuration overrides.
    
    Returns:
        Dictionary of configuration overrides for development
    """
    
    # Check if we're in development mode
    is_dev_mode = os.getenv('DEV_MODE', 'false').lower() == 'true'
    is_debug = os.getenv('WEB_DEBUG', 'false').lower() == 'true'
    
    if not (is_dev_mode or is_debug):
        return {}
    
    return {
        # Disable memory limits for development
        'performance': {
            'max_memory_mb': 2000.0,  # Much higher limit
            'max_memory_percent': 95.0,
            'max_workers': 2,  # Fewer workers to reduce memory usage
            'parallel_chunk_size': 1,
            'memory_monitoring_enabled': False,  # Disable strict monitoring
        },
        
        # More lenient file processing
        'files': {
            'max_file_size_mb': 100,  # Larger files allowed
            'batch_size': 2,  # Smaller batches
            'cleanup_days': 1,  # Clean up daily
            'max_storage_mb': 500,  # More storage
        },
        
        # Relaxed circuit breakers
        'circuit_breakers': {
            'llm_failure_threshold': 10,  # More failures before opening
            'llm_recovery_timeout': 10,   # Faster recovery
            'file_failure_threshold': 10,
            'file_recovery_timeout': 10,
        },
        
        # Development logging
        'logging': {
            'level': 'DEBUG',
            'include_performance_logs': True,
            'memory_warnings_enabled': False,  # Disable memory warnings
        },
        
        # Development features
        'development': {
            'disable_memory_limits': True,
            'verbose_logging': True,
            'auto_cleanup': True,
            'mock_llm_calls': False,  # Set to True to mock LLM for testing
        }
    }


def apply_dev_overrides(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Apply development overrides to configuration.
    
    Args:
        config: Base configuration dictionary
        
    Returns:
        Configuration with development overrides applied
    """
    dev_overrides = get_dev_config_overrides()
    
    if not dev_overrides:
        return config
    
    # Deep merge the overrides
    def deep_merge(base: Dict, override: Dict) -> Dict:
        result = base.copy()
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = deep_merge(result[key], value)
            else:
                result[key] = value
        return result
    
    return deep_merge(config, dev_overrides)


def is_development_mode() -> bool:
    """Check if we're running in development mode."""
    return (
        os.getenv('DEV_MODE', 'false').lower() == 'true' or
        os.getenv('WEB_DEBUG', 'false').lower() == 'true' or
        os.getenv('ENVIRONMENT', 'production').lower() == 'development'
    )


def get_memory_limit_for_function(function_name: str) -> float:
    """
    Get memory limit for a specific function in development mode.
    
    Args:
        function_name: Name of the function
        
    Returns:
        Memory limit in MB (much higher for development)
    """
    if not is_development_mode():
        # Return production limits
        limits = {
            'extract_text': 200.0,
            'process_documents_directly': 300.0,
            'process_expedition': 500.0,
        }
        return limits.get(function_name, 200.0)
    
    # Development limits (much more generous)
    dev_limits = {
        'extract_text': 1500.0,
        'process_documents_directly': 2000.0,
        'process_expedition': 2500.0,
    }
    return dev_limits.get(function_name, 1000.0)
