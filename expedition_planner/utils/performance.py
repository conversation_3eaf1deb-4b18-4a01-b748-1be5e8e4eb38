"""
Performance optimization utilities for expedition planner.
"""

import gc
import logging
import psutil
import threading
import time
import weakref
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import wraps
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple
import multiprocessing as mp

logger = logging.getLogger(__name__)


class MemoryManager:
    """Manages memory usage and cleanup."""
    
    def __init__(self, max_memory_percent: float = 80.0):
        """
        Initialize memory manager.
        
        Args:
            max_memory_percent: Maximum memory usage percentage before cleanup
        """
        self.max_memory_percent = max_memory_percent
        self._weak_refs = weakref.WeakSet()
        self._lock = threading.Lock()
        
    def register_object(self, obj: Any) -> None:
        """Register an object for memory tracking."""
        with self._lock:
            self._weak_refs.add(obj)
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,
            "vms_mb": memory_info.vms / 1024 / 1024,
            "percent": process.memory_percent(),
            "available_mb": psutil.virtual_memory().available / 1024 / 1024,
            "total_mb": psutil.virtual_memory().total / 1024 / 1024
        }
    
    def should_cleanup(self) -> bool:
        """Check if memory cleanup is needed."""
        return self.get_memory_usage()["percent"] > self.max_memory_percent
    
    def force_cleanup(self) -> Dict[str, Any]:
        """Force garbage collection and memory cleanup."""
        initial_memory = self.get_memory_usage()
        
        # Force garbage collection
        collected = gc.collect()
        
        # Clear weak references
        with self._lock:
            self._weak_refs.clear()
        
        final_memory = self.get_memory_usage()
        
        cleanup_stats = {
            "objects_collected": collected,
            "memory_before_mb": initial_memory["rss_mb"],
            "memory_after_mb": final_memory["rss_mb"],
            "memory_freed_mb": initial_memory["rss_mb"] - final_memory["rss_mb"]
        }
        
        logger.info(f"Memory cleanup completed: {cleanup_stats}")
        return cleanup_stats


class ParallelProcessor:
    """Handles parallel processing of documents and tasks."""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize parallel processor.
        
        Args:
            max_workers: Maximum number of worker threads
        """
        self.max_workers = max_workers or min(4, mp.cpu_count())
        self.memory_manager = MemoryManager()
        
    def process_documents_parallel(
        self,
        document_paths: List[Path],
        processor_func: Callable,
        chunk_size: int = 2
    ) -> List[Tuple[Path, Any]]:
        """
        Process documents in parallel with memory management.
        
        Args:
            document_paths: List of document paths to process
            processor_func: Function to process each document
            chunk_size: Number of documents to process in each chunk
            
        Returns:
            List of (path, result) tuples
        """
        results = []
        
        # Process in chunks to manage memory
        for i in range(0, len(document_paths), chunk_size):
            chunk = document_paths[i:i + chunk_size]
            
            # Check memory before processing chunk
            if self.memory_manager.should_cleanup():
                self.memory_manager.force_cleanup()
            
            chunk_results = self._process_chunk_parallel(chunk, processor_func)
            results.extend(chunk_results)
            
            logger.info(f"Processed chunk {i//chunk_size + 1}/{(len(document_paths) + chunk_size - 1)//chunk_size}")
        
        return results
    
    def _process_chunk_parallel(
        self,
        document_paths: List[Path],
        processor_func: Callable
    ) -> List[Tuple[Path, Any]]:
        """Process a chunk of documents in parallel."""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_path = {
                executor.submit(processor_func, path): path
                for path in document_paths
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_path):
                path = future_to_path[future]
                try:
                    result = future.result(timeout=60)  # 1 minute timeout per document
                    results.append((path, result))
                except Exception as e:
                    logger.error(f"Error processing {path}: {e}")
                    results.append((path, {"error": str(e), "success": False}))
        
        return results


class LLMOptimizer:
    """Optimizes LLM calls and context management."""
    
    def __init__(self, max_context_length: int = 4096):
        """
        Initialize LLM optimizer.
        
        Args:
            max_context_length: Maximum context length for LLM
        """
        self.max_context_length = max_context_length
        self._context_cache = {}
        self._cache_lock = threading.Lock()
    
    def optimize_context(self, text: str, preserve_ratio: float = 0.8) -> str:
        """
        Optimize text context for LLM processing.
        
        Args:
            text: Input text to optimize
            preserve_ratio: Ratio of original text to preserve
            
        Returns:
            Optimized text that fits within context limits
        """
        if len(text) <= self.max_context_length:
            return text
        
        # Calculate target length
        target_length = int(self.max_context_length * preserve_ratio)
        
        # Try to preserve important sections
        lines = text.split('\n')
        important_lines = []
        regular_lines = []
        
        for line in lines:
            # Identify important lines (headers, dates, locations, etc.)
            if any(keyword in line.lower() for keyword in [
                'date:', 'location:', 'eta:', 'departure:', 'arrival:',
                'group', 'zodiac', 'equipment', 'schedule', 'tide'
            ]):
                important_lines.append(line)
            else:
                regular_lines.append(line)
        
        # Start with important lines
        result_lines = important_lines[:]
        current_length = sum(len(line) for line in result_lines)
        
        # Add regular lines until we reach target length
        for line in regular_lines:
            if current_length + len(line) <= target_length:
                result_lines.append(line)
                current_length += len(line)
            else:
                break
        
        optimized_text = '\n'.join(result_lines)
        
        if len(optimized_text) > target_length:
            # Truncate if still too long
            optimized_text = optimized_text[:target_length]
        
        logger.debug(f"Context optimized: {len(text)} -> {len(optimized_text)} chars")
        return optimized_text
    
    def cache_context(self, key: str, context: str) -> None:
        """Cache processed context."""
        with self._cache_lock:
            # Limit cache size
            if len(self._context_cache) > 100:
                # Remove oldest entries
                oldest_keys = list(self._context_cache.keys())[:20]
                for old_key in oldest_keys:
                    del self._context_cache[old_key]
            
            self._context_cache[key] = context
    
    def get_cached_context(self, key: str) -> Optional[str]:
        """Get cached context."""
        with self._cache_lock:
            return self._context_cache.get(key)


def performance_monitor(func: Callable) -> Callable:
    """
    Decorator to monitor function performance.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            result = func(*args, **kwargs)
            
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            execution_time = end_time - start_time
            memory_delta = end_memory - start_memory
            
            logger.info(
                f"Performance: {func.__name__} - "
                f"Time: {execution_time:.3f}s, "
                f"Memory: {memory_delta:+.2f}MB",
                extra={
                    "function": func.__name__,
                    "execution_time": execution_time,
                    "memory_delta_mb": memory_delta,
                    "success": True
                }
            )
            
            return result
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.error(
                f"Performance: {func.__name__} failed after {execution_time:.3f}s: {e}",
                extra={
                    "function": func.__name__,
                    "execution_time": execution_time,
                    "success": False,
                    "error": str(e)
                }
            )
            
            raise
    
    return wrapper


def memory_efficient(max_memory_mb: float = 500.0):
    """
    Decorator to ensure functions don't exceed memory limits.
    
    Args:
        max_memory_mb: Maximum memory usage in MB
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            memory_manager = MemoryManager(max_memory_percent=75.0)
            
            # Check memory before execution
            initial_memory = memory_manager.get_memory_usage()
            if initial_memory["rss_mb"] > max_memory_mb:
                memory_manager.force_cleanup()
            
            try:
                result = func(*args, **kwargs)
                
                # Check memory after execution
                final_memory = memory_manager.get_memory_usage()
                if final_memory["rss_mb"] > max_memory_mb:
                    logger.warning(
                        f"Function {func.__name__} exceeded memory limit: "
                        f"{final_memory['rss_mb']:.2f}MB > {max_memory_mb}MB"
                    )
                    memory_manager.force_cleanup()
                
                return result
                
            except MemoryError:
                logger.error(f"Memory error in {func.__name__}, forcing cleanup")
                memory_manager.force_cleanup()
                raise
        
        return wrapper
    return decorator


# Global instances
memory_manager = MemoryManager()
parallel_processor = ParallelProcessor()
llm_optimizer = LLMOptimizer()


def get_performance_stats() -> Dict[str, Any]:
    """Get current performance statistics."""
    return {
        "memory": memory_manager.get_memory_usage(),
        "cpu_percent": psutil.cpu_percent(interval=1),
        "cpu_count": psutil.cpu_count(),
        "parallel_workers": parallel_processor.max_workers,
        "llm_context_limit": llm_optimizer.max_context_length,
        "cache_size": len(llm_optimizer._context_cache)
    }
