"""
End-to-end tests for the web interface.
"""

import pytest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import threading
import time

from expedition_planner.web.langchain_app import create_langchain_app


class TestWebInterface:
    """Test the Flask web interface."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask app."""
        config_override = {
            "testing": True,
            "secret_key": "test-secret-key",
            "directories": {
                "uploads": tempfile.mkdtemp(prefix="test_uploads_"),
                "outputs": tempfile.mkdtemp(prefix="test_outputs_")
            }
        }
        
        app = create_langchain_app(config_override)
        app.config['TESTING'] = True
        
        yield app
        
        # Cleanup
        shutil.rmtree(config_override["directories"]["uploads"])
        shutil.rmtree(config_override["directories"]["outputs"])
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    def test_index_page_loads(self, client):
        """Test that the index page loads successfully."""
        response = client.get('/')
        assert response.status_code == 200
        # Note: This might fail if template doesn't exist, which is expected
    
    def test_agent_interface_page(self, client):
        """Test that the agent interface page loads."""
        response = client.get('/agent-interface')
        # Note: This might fail if template doesn't exist, which is expected
        assert response.status_code in [200, 404]  # 404 is acceptable if template missing
    
    @patch('expedition_planner.web.langchain_app.langchain_processor')
    def test_file_upload_endpoint(self, mock_processor, client, app):
        """Test file upload endpoint."""
        # Create test file
        test_content = b"Test expedition document content"
        
        # Mock processor
        mock_processor.process_expedition.return_value = {
            "success": True,
            "summary": {"json_files_generated": 1}
        }
        
        # Test upload
        from io import BytesIO
        response = client.post('/api/upload-documents', data={
            'files': (BytesIO(test_content), 'test_expedition.txt'),
        }, content_type='multipart/form-data')
        
        # Should return JSON response
        assert response.status_code in [200, 400, 500]  # Various acceptable responses
        
        if response.status_code == 200:
            data = json.loads(response.data)
            assert "session_id" in data
    
    def test_status_endpoint(self, client):
        """Test status endpoint."""
        response = client.get('/api/status/test-session-id')
        
        # Should return JSON response (might be error for non-existent session)
        assert response.status_code in [200, 404]
        
        if response.status_code == 200:
            data = json.loads(response.data)
            assert isinstance(data, dict)
    
    def test_download_endpoint(self, client, app):
        """Test file download endpoint."""
        # Create a test file in outputs directory
        outputs_dir = Path(app.config["directories"]["outputs"])
        test_file = outputs_dir / "test_output.json"
        test_file.write_text('{"test": "data"}')
        
        response = client.get(f'/api/download/{test_file}')
        
        # Should either download the file or return 404
        assert response.status_code in [200, 404]
    
    def test_capabilities_endpoint(self, client):
        """Test capabilities endpoint."""
        response = client.get('/api/capabilities')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "supported_formats" in data
        assert "agents" in data


class TestWebSocketInterface:
    """Test WebSocket functionality."""
    
    @pytest.fixture
    def socketio_client(self, app):
        """Create SocketIO test client."""
        try:
            from flask_socketio import SocketIOTestClient
            from expedition_planner.web.langchain_app import socketio

            if socketio:
                return SocketIOTestClient(app, socketio)
            else:
                pytest.skip("SocketIO not available")
        except ImportError:
            pytest.skip("SocketIO test client not available")
    
    def test_websocket_connection(self, socketio_client):
        """Test WebSocket connection."""
        if socketio_client:
            received = socketio_client.get_received()
            # Should receive connection confirmation
            assert len(received) >= 0  # May or may not receive immediate messages
    
    def test_websocket_join_session(self, socketio_client):
        """Test joining a processing session via WebSocket."""
        if socketio_client:
            # Emit join session event
            socketio_client.emit('join_session', {'session_id': 'test-session-123'})
            
            # Should handle the event without error
            received = socketio_client.get_received()
            # Verify no error messages
            for message in received:
                assert 'error' not in message.get('name', '')


class TestWebInterfaceErrorHandling:
    """Test error handling in the web interface."""
    
    @pytest.fixture
    def app_with_errors(self):
        """Create app configured to trigger errors."""
        config_override = {
            "testing": True,
            "directories": {
                "uploads": "/nonexistent/uploads",  # Invalid directory
                "outputs": "/nonexistent/outputs"   # Invalid directory
            }
        }
        
        return create_langchain_app(config_override)
    
    def test_upload_with_invalid_directory(self, app_with_errors):
        """Test upload handling with invalid directories."""
        client = app_with_errors.test_client()
        
        from io import BytesIO
        response = client.post('/api/upload-documents', data={
            'files': (BytesIO(b"test content"), 'test.txt'),
        }, content_type='multipart/form-data')
        
        # Should handle the error gracefully
        assert response.status_code in [400, 500]
    
    def test_download_nonexistent_file(self, client):
        """Test downloading non-existent file."""
        response = client.get('/api/download/nonexistent_file.json')
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert "error" in data
    
    def test_status_nonexistent_session(self, client):
        """Test getting status for non-existent session."""
        response = client.get('/api/status/nonexistent-session-id')
        
        assert response.status_code in [200, 404]
        if response.status_code == 200:
            data = json.loads(response.data)
            assert "error" in data or "session_id" in data


class TestWebInterfacePerformance:
    """Test performance aspects of the web interface."""
    
    def test_concurrent_uploads(self, client):
        """Test handling of concurrent uploads."""
        import concurrent.futures
        
        def upload_file(file_content, filename):
            from io import BytesIO
            return client.post('/api/upload-documents', data={
                'files': (BytesIO(file_content), filename),
            }, content_type='multipart/form-data')
        
        # Create multiple upload requests
        files = [(b"test content", f'test_{i}.txt') for i in range(3)]
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(upload_file, content, filename) 
                      for content, filename in files]
            
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should complete (may succeed or fail gracefully)
        assert len(results) == 3
        for result in results:
            assert result.status_code in [200, 400, 500]
    
    def test_large_file_upload(self, client):
        """Test uploading large files."""
        # Create a large test file (1MB)
        large_content = b"x" * (1024 * 1024)
        
        from io import BytesIO
        response = client.post('/api/upload-documents', data={
            'files': (BytesIO(large_content), 'large_test.txt'),
        }, content_type='multipart/form-data')
        
        # Should handle large files (may succeed or fail with appropriate error)
        assert response.status_code in [200, 400, 413, 500]  # 413 = Request Entity Too Large
    
    def test_multiple_status_requests(self, client):
        """Test handling multiple status requests."""
        session_ids = ['session-1', 'session-2', 'session-3']
        
        responses = []
        for session_id in session_ids:
            response = client.get(f'/api/status/{session_id}')
            responses.append(response)
        
        # All requests should complete
        assert len(responses) == 3
        for response in responses:
            assert response.status_code in [200, 404]


class TestWebInterfaceAccessibility:
    """Test accessibility and usability of the web interface."""
    
    def test_cors_headers(self, client):
        """Test CORS headers for API endpoints."""
        response = client.get('/api/capabilities')
        
        # Check if CORS headers are present (optional)
        headers = response.headers
        # CORS headers might not be set, which is acceptable
        assert response.status_code == 200
    
    def test_content_type_headers(self, client):
        """Test proper content type headers."""
        response = client.get('/api/capabilities')
        
        assert response.status_code == 200
        assert 'application/json' in response.content_type
    
    def test_error_response_format(self, client):
        """Test that error responses are properly formatted."""
        response = client.get('/api/status/nonexistent-session')
        
        # Should return JSON even for errors
        assert 'application/json' in response.content_type
        
        if response.status_code != 200:
            data = json.loads(response.data)
            assert isinstance(data, dict)
            # Should have error information
            assert "error" in data or "message" in data
