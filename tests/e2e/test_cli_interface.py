"""
End-to-end tests for the CLI interface.
"""

import pytest
import tempfile
import shutil
import subprocess
import sys
from pathlib import Path
from unittest.mock import Mock, patch
from click.testing import CliRunner

from expedition_planner.cli.langchain_main import cli


class TestCLIInterface:
    """Test the command-line interface."""
    
    @pytest.fixture
    def runner(self):
        """Create CLI test runner."""
        return CliRunner()
    
    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace for CLI tests."""
        temp_dir = tempfile.mkdtemp(prefix="cli_test_")
        workspace = Path(temp_dir)
        
        # Create test documents
        docs_dir = workspace / "documents"
        docs_dir.mkdir()
        
        test_doc = docs_dir / "test_expedition.txt"
        test_doc.write_text("""
        Expedition Report - Day 1
        Location: The Lacepedes
        Date: 2024-07-12
        ETA: 07:40
        
        Equipment:
        - 8 Zodiacs
        - 1 Twin
        
        Groups:
        Yellow Group - Departure: 07:40, Return: 09:40
        """)
        
        yield workspace
        
        # Cleanup
        shutil.rmtree(temp_dir)
    
    def test_cli_help(self, runner):
        """Test CLI help command."""
        result = runner.invoke(cli, ['--help'])
        
        assert result.exit_code == 0
        assert 'LangChain-based Expedition Document Processor' in result.output
    
    def test_cli_version(self, runner):
        """Test CLI version command."""
        result = runner.invoke(cli, ['--version'])
        
        assert result.exit_code == 0
        assert '2.0.0' in result.output
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_process_command(self, mock_processor, runner, temp_workspace):
        """Test the process command."""
        # Setup mock processor
        mock_processor_instance = Mock()
        mock_processor_instance.validate_configuration.return_value = {
            'valid': True,
            'issues': [],
            'warnings': []
        }
        mock_processor_instance.process_expedition.return_value = {
            'success': True,
            'status': 'completed',
            'summary': {
                'documents_processed': 1,
                'location_groups': 1,
                'json_files_generated': 1,
                'processing_time': '30.5s',
                'output_directory': str(temp_workspace / "output")
            }
        }
        mock_processor.return_value = mock_processor_instance
        
        # Test process command
        result = runner.invoke(cli, [
            'process',
            str(temp_workspace / "documents"),
            '--expedition-name', 'Test Expedition',
            '--output-dir', str(temp_workspace / "output")
        ])
        
        assert result.exit_code == 0
        assert 'Starting LangChain expedition processing' in result.output
        assert 'Processing completed successfully' in result.output
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_process_command_with_validation_failure(self, mock_processor, runner, temp_workspace):
        """Test process command with validation failure."""
        # Setup mock processor with validation failure
        mock_processor_instance = Mock()
        mock_processor_instance.validate_configuration.return_value = {
            'valid': False,
            'issues': ['Ollama not available', 'Invalid model configuration'],
            'warnings': []
        }
        mock_processor.return_value = mock_processor_instance
        
        # Test process command
        result = runner.invoke(cli, [
            'process',
            str(temp_workspace / "documents"),
            '--expedition-name', 'Test Expedition'
        ])
        
        assert result.exit_code == 1
        assert 'Configuration validation failed' in result.output
        assert 'Ollama not available' in result.output
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_organize_command(self, mock_processor, runner, temp_workspace):
        """Test the organize command."""
        # Setup mock processor
        mock_processor_instance = Mock()
        mock_processor_instance.process_document_folder.return_value = {
            'success': True,
            'organization': {
                'groups': {
                    'The Lacepedes': ['test_expedition.txt']
                }
            },
            'recommendations': {
                'recommended_order': ['The Lacepedes']
            }
        }
        mock_processor.return_value = mock_processor_instance
        
        # Test organize command
        result = runner.invoke(cli, [
            'organize',
            str(temp_workspace / "documents"),
            '--grouping', 'location'
        ])
        
        assert result.exit_code == 0
        assert 'Organizing documents' in result.output
        assert 'Organization completed successfully' in result.output
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_validate_command(self, mock_processor, runner):
        """Test the validate command."""
        # Setup mock processor
        mock_processor_instance = Mock()
        mock_processor_instance.validate_configuration.return_value = {
            'valid': True,
            'issues': [],
            'warnings': ['Model not optimized for this task'],
            'ollama_status': 'connected',
            'docling_status': 'available'
        }
        mock_processor.return_value = mock_processor_instance
        
        # Test validate command
        result = runner.invoke(cli, ['validate'])
        
        assert result.exit_code == 0
        assert 'Configuration is valid' in result.output
        assert 'Ollama: Connected' in result.output
        assert 'Docling: Available' in result.output
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_capabilities_command(self, mock_processor, runner):
        """Test the capabilities command."""
        # Setup mock processor
        mock_processor_instance = Mock()
        mock_processor_instance.get_capabilities.return_value = {
            'supported_formats': ['.pdf', '.docx', '.txt'],
            'llm_model': 'mistral-7b-v0-1-gguf:latest',
            'offline_operation': True,
            'agents': ['Document Organizer', 'Data Extractor']
        }
        mock_processor.return_value = mock_processor_instance
        
        # Test capabilities command
        result = runner.invoke(cli, ['capabilities'])
        
        assert result.exit_code == 0
        assert 'LangChain Expedition Processor Capabilities' in result.output
        assert 'mistral-7b-v0-1-gguf:latest' in result.output
        assert 'Offline Operation: Yes' in result.output
    
    @patch('expedition_planner.cli.langchain_main.run_langchain_app')
    def test_web_command(self, mock_run_app, runner):
        """Test the web command."""
        # Mock the web app runner
        mock_run_app.return_value = None
        
        # Test web command (will be interrupted by KeyboardInterrupt simulation)
        with patch('expedition_planner.cli.langchain_main.run_langchain_app', side_effect=KeyboardInterrupt):
            result = runner.invoke(cli, ['web', '--host', '127.0.0.1', '--port', '8081'])
        
        assert result.exit_code == 0
        assert 'Starting LangChain web interface' in result.output
    
    def test_process_command_with_nonexistent_directory(self, runner):
        """Test process command with non-existent directory."""
        result = runner.invoke(cli, [
            'process',
            '/nonexistent/directory',
            '--expedition-name', 'Test'
        ])
        
        # Should fail with appropriate error
        assert result.exit_code != 0
    
    def test_organize_command_with_invalid_grouping(self, runner, temp_workspace):
        """Test organize command with invalid grouping method."""
        result = runner.invoke(cli, [
            'organize',
            str(temp_workspace / "documents"),
            '--grouping', 'invalid_method'
        ])
        
        # Should fail with validation error
        assert result.exit_code != 0
    
    def test_verbose_flag(self, runner, temp_workspace):
        """Test verbose flag functionality."""
        with patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor') as mock_processor:
            mock_processor_instance = Mock()
            mock_processor_instance.validate_configuration.return_value = {'valid': True, 'issues': [], 'warnings': []}
            mock_processor_instance.process_expedition.return_value = {'success': True, 'summary': {}}
            mock_processor.return_value = mock_processor_instance
            
            result = runner.invoke(cli, [
                'process',
                str(temp_workspace / "documents"),
                '--verbose'
            ])
            
            # Verbose flag should not cause errors
            assert result.exit_code == 0


class TestCLIErrorHandling:
    """Test CLI error handling."""
    
    @pytest.fixture
    def runner(self):
        """Create CLI test runner."""
        return CliRunner()
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_process_command_exception(self, mock_processor, runner):
        """Test process command with unexpected exception."""
        # Setup mock to raise exception
        mock_processor.side_effect = Exception("Unexpected error")
        
        result = runner.invoke(cli, [
            'process',
            '/some/directory',
            '--expedition-name', 'Test'
        ])
        
        assert result.exit_code == 1
        assert 'Error:' in result.output
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_organize_command_exception(self, mock_processor, runner):
        """Test organize command with unexpected exception."""
        # Setup mock to raise exception
        mock_processor_instance = Mock()
        mock_processor_instance.process_document_folder.side_effect = Exception("Organization failed")
        mock_processor.return_value = mock_processor_instance
        
        result = runner.invoke(cli, [
            'organize',
            '/some/directory'
        ])
        
        assert result.exit_code == 1
        assert 'Error:' in result.output
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_validate_command_exception(self, mock_processor, runner):
        """Test validate command with unexpected exception."""
        # Setup mock to raise exception
        mock_processor_instance = Mock()
        mock_processor_instance.validate_configuration.side_effect = Exception("Validation failed")
        mock_processor.return_value = mock_processor_instance
        
        result = runner.invoke(cli, ['validate'])
        
        assert result.exit_code == 1
        assert 'Error:' in result.output


class TestCLIIntegration:
    """Test CLI integration with actual components."""
    
    @pytest.fixture
    def runner(self):
        """Create CLI test runner."""
        return CliRunner()
    
    def test_cli_import_structure(self):
        """Test that CLI can import all required modules."""
        try:
            from expedition_planner.cli.langchain_main import cli
            from expedition_planner.core.langchain_processor import LangChainExpeditionProcessor
            from expedition_planner.web.langchain_app import run_langchain_app
            
            # If we get here, imports are working
            assert True
        except ImportError as e:
            pytest.fail(f"CLI import failed: {e}")
    
    def test_cli_configuration_access(self, runner):
        """Test that CLI can access configuration."""
        try:
            from expedition_planner.config.langchain_config import get_langchain_config
            
            config = get_langchain_config()
            assert isinstance(config, dict)
            assert "ollama" in config
            
        except Exception as e:
            pytest.fail(f"Configuration access failed: {e}")
    
    @patch('expedition_planner.cli.langchain_main.LangChainExpeditionProcessor')
    def test_cli_processor_integration(self, mock_processor, runner):
        """Test CLI integration with processor."""
        # Setup mock
        mock_processor_instance = Mock()
        mock_processor_instance.validate_configuration.return_value = {
            'valid': True,
            'issues': [],
            'warnings': []
        }
        mock_processor.return_value = mock_processor_instance
        
        # Test that CLI can create and use processor
        result = runner.invoke(cli, ['validate'])
        
        assert result.exit_code == 0
        mock_processor.assert_called_once()
        mock_processor_instance.validate_configuration.assert_called_once()
