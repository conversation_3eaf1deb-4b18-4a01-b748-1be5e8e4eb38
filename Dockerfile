# Multi-stage Dockerfile for Expedition Planner
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    tesseract-ocr \
    tesseract-ocr-eng \
    poppler-utils \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY pyproject.toml uv.lock ./
RUN pip install uv && \
    uv sync --frozen --no-dev

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/expedition_planner/uploads \
             /app/consolidated_outputs \
             /app/logs && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/validate-configuration || exit 1

# Default command
CMD ["python", "-m", "expedition_planner.web.langchain_app"]

# Development stage
FROM base as development

USER root
RUN uv sync --frozen
USER appuser

ENV WEB_DEBUG=true \
    LOG_LEVEL=DEBUG

# Production stage
FROM base as production

ENV WEB_DEBUG=false \
    LOG_LEVEL=INFO \
    WEB_HOST=0.0.0.0 \
    WEB_PORT=8080

# Copy only production files
COPY --from=base /app /app

# Run as non-root user
USER appuser

CMD ["python", "-m", "expedition_planner.web.langchain_app"]
