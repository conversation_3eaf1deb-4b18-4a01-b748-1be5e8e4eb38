# Prometheus alert rules for Expedition Planner

groups:
  - name: expedition_planner_alerts
    rules:
      # Application availability alerts
      - alert: ExpeditionPlannerDown
        expr: up{job="expedition-planner"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Expedition Planner application is down"
          description: "Expedition Planner has been down for more than 1 minute."

      - alert: OllamaServiceDown
        expr: up{job="ollama"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Ollama LLM service is down"
          description: "Ollama service has been down for more than 2 minutes."

      # Performance alerts
      - alert: HighMemoryUsage
        expr: (expedition_planner_memory_usage_mb / expedition_planner_memory_limit_mb) > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 90% for more than 5 minutes."

      - alert: HighCPUUsage
        expr: expedition_planner_cpu_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes."

      # Storage alerts
      - alert: HighStorageUsage
        expr: (expedition_planner_storage_used_mb / expedition_planner_storage_limit_mb) > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High storage usage detected"
          description: "Storage usage is above 80% of the limit."

      - alert: StorageFull
        expr: (expedition_planner_storage_used_mb / expedition_planner_storage_limit_mb) > 0.95
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Storage nearly full"
          description: "Storage usage is above 95% of the limit."

      # Circuit breaker alerts
      - alert: LLMCircuitBreakerOpen
        expr: expedition_planner_circuit_breaker_state{service="llm"} == 1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "LLM circuit breaker is open"
          description: "LLM service circuit breaker has been open for more than 1 minute."

      - alert: FileProcessingCircuitBreakerOpen
        expr: expedition_planner_circuit_breaker_state{service="file_processing"} == 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "File processing circuit breaker is open"
          description: "File processing circuit breaker has been open for more than 2 minutes."

      # Error rate alerts
      - alert: HighErrorRate
        expr: rate(expedition_planner_errors_total[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 10% for more than 3 minutes."

      # Response time alerts
      - alert: SlowResponseTime
        expr: expedition_planner_response_time_seconds > 30
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Slow response times detected"
          description: "Average response time is above 30 seconds for more than 2 minutes."

      # File processing alerts
      - alert: ProcessingQueueBacklog
        expr: expedition_planner_processing_queue_size > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Processing queue backlog"
          description: "More than 10 files are waiting in the processing queue for more than 5 minutes."

      # WebSocket connection alerts
      - alert: WebSocketConnectionFailures
        expr: rate(expedition_planner_websocket_failures_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High WebSocket connection failure rate"
          description: "WebSocket connection failure rate is above 5% for more than 2 minutes."

  - name: infrastructure_alerts
    rules:
      # Redis alerts (if enabled)
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis service is down"
          description: "Redis service has been down for more than 1 minute."

      # Nginx alerts
      - alert: NginxDown
        expr: up{job="nginx"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Nginx reverse proxy is down"
          description: "Nginx has been down for more than 1 minute."

      # System resource alerts
      - alert: HighSystemMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High system memory usage"
          description: "System memory usage is above 90% for more than 5 minutes."

      - alert: HighSystemCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High system CPU usage"
          description: "System CPU usage is above 80% for more than 5 minutes."

      - alert: LowDiskSpace
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space"
          description: "Disk usage is above 80% on {{ $labels.mountpoint }}."

      - alert: DiskSpaceCritical
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) > 0.95
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical disk space"
          description: "Disk usage is above 95% on {{ $labels.mountpoint }}."
