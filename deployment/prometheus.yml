# Prometheus configuration for Expedition Planner monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # Expedition Planner application metrics
  - job_name: 'expedition-planner'
    static_configs:
      - targets: ['expedition-planner:9091']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Expedition Planner health checks
  - job_name: 'expedition-planner-health'
    static_configs:
      - targets: ['expedition-planner:8080']
    metrics_path: '/api/performance-stats'
    scrape_interval: 60s
    scrape_timeout: 30s

  # Ollama service monitoring
  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
    metrics_path: '/api/tags'
    scrape_interval: 60s
    scrape_timeout: 10s

  # Redis monitoring (if enabled)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Nginx monitoring
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
