# 🚀 Expedition Planner Deployment Guide

Complete guide for deploying the Expedition Planner application in production, staging, and development environments.

## 📋 **Prerequisites**

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows with WSL2
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 20GB free space
- **CPU**: 2+ cores recommended

### Required Software
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Git**: For cloning the repository
- **curl**: For health checks and API testing

### Optional (for advanced monitoring)
- **Prometheus**: For metrics collection
- **Grafana**: For dashboards
- **Nginx**: For reverse proxy (included in Docker setup)

---

## 🏗️ **Quick Start Deployment**

### 1. Clone and Setup
```bash
# Clone the repository
git clone https://github.com/GojiBarry/File.git
cd File

# Copy environment configuration
cp .env.example .env

# Edit configuration (see Configuration section below)
nano .env
```

### 2. Setup Ollama (Required)
```bash
# Setup Ollama with required models
./scripts/setup-ollama.sh

# Or manually:
# docker run -d -p 11434:11434 ollama/ollama
# docker exec -it <container> ollama pull mistral-7b-v0-1-gguf:latest
```

### 3. Deploy Application
```bash
# Deploy with automatic setup
./scripts/deploy.sh production --build --health

# Or manually with Docker Compose
docker-compose up -d --build
```

### 4. Verify Deployment
```bash
# Check application health
curl http://localhost:8080/api/validate-configuration

# Check Ollama service
curl http://localhost:11434/api/tags

# View logs
docker-compose logs -f expedition-planner
```

**🎉 Your application is now running at: http://localhost:8080**

---

## ⚙️ **Configuration**

### Environment Variables (.env file)

#### **Essential Settings**
```bash
# Application
WEB_HOST=0.0.0.0
WEB_PORT=8080
SECRET_KEY=your-super-secret-key-change-this

# Ollama LLM
OLLAMA_BASE_URL=http://ollama:11434
OLLAMA_MODEL=mistral-7b-v0-1-gguf:latest

# File Processing
MAX_FILE_SIZE_MB=50
CLEANUP_DAYS=7
```

#### **Production Settings**
```bash
# Security
WEB_DEBUG=false
LOG_LEVEL=INFO
SSL_ENABLED=true

# Performance
MAX_WORKERS=4
MAX_MEMORY_MB=500

# Monitoring
ENABLE_METRICS=true
```

#### **Development Settings**
```bash
# Debug
WEB_DEBUG=true
LOG_LEVEL=DEBUG

# Development features
DEV_MODE=true
DEV_AUTO_RELOAD=true
```

---

## 🌍 **Environment-Specific Deployments**

### Development Environment
```bash
# Use development configuration
cp .env.example .env.development

# Deploy for development
./scripts/deploy.sh development --logs

# Access at: http://localhost:8080
```

### Staging Environment
```bash
# Use staging configuration
cp .env.example .env.staging

# Deploy for staging
./scripts/deploy.sh staging --build --health

# Access at: http://your-staging-server:8080
```

### Production Environment
```bash
# Use production configuration
cp .env.example .env.production

# Generate SSL certificates
./scripts/generate-ssl.sh your-domain.com

# Deploy for production
./scripts/deploy.sh production --backup --build --health

# Access at: https://your-domain.com
```

---

## 🔒 **Security Setup**

### SSL/TLS Configuration

#### Generate Self-Signed Certificates (Development)
```bash
./scripts/generate-ssl.sh expedition-planner.local
```

#### Production SSL Setup
1. **Option A: Let's Encrypt (Recommended)**
```bash
# Install certbot
sudo apt install certbot

# Generate certificate
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem deployment/ssl/expedition_planner.crt
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem deployment/ssl/private/expedition_planner.key
```

2. **Option B: Commercial Certificate**
- Purchase SSL certificate from trusted CA
- Place certificate files in `deployment/ssl/`
- Update nginx configuration

### Security Best Practices
```bash
# Set strong secret key
SECRET_KEY=$(openssl rand -base64 32)

# Enable security headers (already configured in nginx)
# - HSTS
# - X-Frame-Options
# - X-Content-Type-Options
# - X-XSS-Protection

# Configure firewall
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

---

## 📊 **Monitoring and Observability**

### Built-in Monitoring
- **Health Checks**: `/api/validate-configuration`
- **Performance Stats**: `/api/performance-stats`
- **Storage Status**: `/api/storage-status`
- **Circuit Breaker Status**: Included in performance stats

### Advanced Monitoring (Optional)
```bash
# Deploy with monitoring stack
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d

# Access monitoring dashboards
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3000 (admin/admin)
```

### Log Management
```bash
# View application logs
docker-compose logs -f expedition-planner

# View all service logs
docker-compose logs -f

# Export logs
docker-compose logs expedition-planner > expedition_planner.log
```

---

## 💾 **Backup and Recovery**

### Automated Backups
```bash
# Create full backup
./scripts/backup.sh --full

# Create data-only backup
./scripts/backup.sh --data-only

# Schedule daily backups (crontab)
0 2 * * * /path/to/scripts/backup.sh --full
0 4 * * * /path/to/scripts/backup.sh --cleanup
```

### Manual Backup Operations
```bash
# List available backups
./scripts/backup.sh --list

# Restore from backup
./scripts/backup.sh --restore backup_file.tar.gz

# Cleanup old backups
./scripts/backup.sh --cleanup
```

### Backup Strategy
- **Daily**: Full backup at 2 AM
- **Retention**: 30 days (configurable)
- **Storage**: Local filesystem (extend to cloud storage)

---

## 🔧 **Maintenance**

### Regular Maintenance Tasks
```bash
# Update application
git pull origin main
./scripts/deploy.sh production --build

# Clean up old files
./scripts/backup.sh --cleanup
docker system prune -f

# Update Ollama models
./scripts/setup-ollama.sh --model mistral:latest

# Check system health
curl http://localhost:8080/api/performance-stats
```

### Scaling Considerations
```bash
# Increase worker processes
MAX_WORKERS=8

# Increase memory limits
MAX_MEMORY_MB=1000

# Add load balancer (nginx upstream)
# Configure multiple application instances
```

---

## 🚨 **Troubleshooting**

### Common Issues

#### Application Won't Start
```bash
# Check logs
docker-compose logs expedition-planner

# Verify configuration
./scripts/deploy.sh production --health

# Check port conflicts
netstat -tulpn | grep :8080
```

#### Ollama Connection Issues
```bash
# Check Ollama service
curl http://localhost:11434/api/tags

# Restart Ollama
docker-compose restart ollama

# Pull required model
./scripts/setup-ollama.sh
```

#### High Memory Usage
```bash
# Check performance stats
curl http://localhost:8080/api/performance-stats

# Trigger cleanup
curl -X POST http://localhost:8080/api/cleanup-files

# Restart application
docker-compose restart expedition-planner
```

#### SSL Certificate Issues
```bash
# Regenerate certificates
./scripts/generate-ssl.sh your-domain.com

# Check certificate validity
openssl x509 -in deployment/ssl/expedition_planner.crt -text -noout

# Update nginx configuration
docker-compose restart nginx
```

### Performance Optimization
```bash
# Monitor resource usage
docker stats

# Optimize Docker images
docker-compose build --no-cache

# Clean up unused resources
docker system prune -a
```

---

## 📞 **Support and Maintenance**

### Health Monitoring
- Set up monitoring alerts for critical metrics
- Configure log aggregation for centralized logging
- Implement automated health checks

### Update Strategy
1. **Test in staging environment**
2. **Create backup before deployment**
3. **Deploy during maintenance window**
4. **Verify functionality post-deployment**
5. **Monitor for issues**

### Emergency Procedures
```bash
# Quick rollback
./scripts/deploy.sh production --rollback

# Emergency stop
docker-compose down

# Emergency restore
./scripts/backup.sh --restore latest_backup.tar.gz
```

---

## 🎯 **Production Checklist**

### Pre-Deployment
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Firewall rules configured
- [ ] Backup strategy implemented
- [ ] Monitoring setup completed
- [ ] DNS records updated

### Post-Deployment
- [ ] Health checks passing
- [ ] SSL certificate valid
- [ ] Monitoring alerts configured
- [ ] Backup tested
- [ ] Performance baseline established
- [ ] Documentation updated

### Ongoing Maintenance
- [ ] Regular security updates
- [ ] Certificate renewal (Let's Encrypt: every 90 days)
- [ ] Backup verification
- [ ] Performance monitoring
- [ ] Log rotation
- [ ] Capacity planning

---

**🎉 Congratulations! Your Expedition Planner is now production-ready!**

For additional support or questions, refer to the application logs and monitoring dashboards.
