#!/bin/bash

# Backup Script for Expedition Planner
# Creates comprehensive backups of application data and configuration

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${BACKUP_DIR:-$PROJECT_ROOT/backups}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.yml}"
ENV_FILE="${ENV_FILE:-.env}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Expedition Planner Backup Script

Usage: $0 [options]

Options:
    --full          Create full backup (default)
    --data-only     Backup only data files
    --config-only   Backup only configuration files
    --restore FILE  Restore from backup file
    --list          List available backups
    --cleanup       Remove old backups based on retention policy
    --help          Show this help message

Environment Variables:
    BACKUP_DIR      Backup directory (default: ./backups)
    RETENTION_DAYS  Days to keep backups (default: 30)
    COMPOSE_FILE    Docker compose file (default: docker-compose.yml)
    ENV_FILE        Environment file (default: .env)

Examples:
    $0                              # Create full backup
    $0 --data-only                  # Backup only data
    $0 --restore backup_20240101_120000.tar.gz
    $0 --cleanup                    # Remove old backups

EOF
}

# Create backup directory
create_backup_dir() {
    mkdir -p "$BACKUP_DIR"
    log_info "Backup directory: $BACKUP_DIR"
}

# Generate backup filename
generate_backup_filename() {
    local backup_type="${1:-full}"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    echo "expedition_planner_${backup_type}_${timestamp}.tar.gz"
}

# Check if containers are running
check_containers() {
    if ! docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" ps | grep -q "Up"; then
        log_warning "No running containers found. Some backup operations may not work."
        return 1
    fi
    return 0
}

# Backup application data
backup_data() {
    local temp_dir="$BACKUP_DIR/temp_$(date +%s)"
    mkdir -p "$temp_dir/data"
    
    log_info "Backing up application data..."
    
    # Backup uploads directory
    if [[ -d "$PROJECT_ROOT/expedition_planner/uploads" ]]; then
        log_info "Backing up uploads directory..."
        cp -r "$PROJECT_ROOT/expedition_planner/uploads" "$temp_dir/data/"
    fi
    
    # Backup consolidated outputs
    if [[ -d "$PROJECT_ROOT/consolidated_outputs" ]]; then
        log_info "Backing up consolidated outputs..."
        cp -r "$PROJECT_ROOT/consolidated_outputs" "$temp_dir/data/"
    fi
    
    # Backup logs if they exist
    if [[ -d "$PROJECT_ROOT/logs" ]]; then
        log_info "Backing up logs..."
        cp -r "$PROJECT_ROOT/logs" "$temp_dir/data/"
    fi
    
    # Backup Docker volumes if containers are running
    if check_containers; then
        log_info "Backing up Docker volumes..."
        
        # Create volume backup directory
        mkdir -p "$temp_dir/volumes"
        
        # Backup expedition uploads volume
        docker run --rm \
            -v expedition_uploads:/source:ro \
            -v "$temp_dir/volumes":/backup \
            alpine tar czf /backup/expedition_uploads.tar.gz -C /source .
        
        # Backup expedition outputs volume
        docker run --rm \
            -v expedition_outputs:/source:ro \
            -v "$temp_dir/volumes":/backup \
            alpine tar czf /backup/expedition_outputs.tar.gz -C /source .
        
        # Backup expedition logs volume
        docker run --rm \
            -v expedition_logs:/source:ro \
            -v "$temp_dir/volumes":/backup \
            alpine tar czf /backup/expedition_logs.tar.gz -C /source .
    fi
    
    echo "$temp_dir"
}

# Backup configuration files
backup_config() {
    local temp_dir="$1"
    mkdir -p "$temp_dir/config"
    
    log_info "Backing up configuration files..."
    
    # Backup environment files
    for env_file in .env .env.* .env-*; do
        if [[ -f "$PROJECT_ROOT/$env_file" ]]; then
            cp "$PROJECT_ROOT/$env_file" "$temp_dir/config/"
        fi
    done
    
    # Backup Docker compose files
    for compose_file in docker-compose*.yml docker-compose*.yaml; do
        if [[ -f "$PROJECT_ROOT/$compose_file" ]]; then
            cp "$PROJECT_ROOT/$compose_file" "$temp_dir/config/"
        fi
    done
    
    # Backup deployment configuration
    if [[ -d "$PROJECT_ROOT/deployment" ]]; then
        cp -r "$PROJECT_ROOT/deployment" "$temp_dir/config/"
    fi
    
    # Backup SSL certificates (excluding private keys for security)
    if [[ -d "$PROJECT_ROOT/deployment/ssl" ]]; then
        mkdir -p "$temp_dir/config/ssl"
        find "$PROJECT_ROOT/deployment/ssl" -name "*.crt" -o -name "*.pem" | while read -r file; do
            cp "$file" "$temp_dir/config/ssl/"
        done
    fi
    
    # Create backup metadata
    cat > "$temp_dir/backup_metadata.json" << EOF
{
    "backup_date": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "backup_type": "${BACKUP_TYPE:-full}",
    "application_version": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "docker_compose_file": "$COMPOSE_FILE",
    "environment_file": "$ENV_FILE",
    "backup_script_version": "1.0"
}
EOF
}

# Create full backup
create_full_backup() {
    log_info "Creating full backup..."
    
    local temp_dir=$(backup_data)
    backup_config "$temp_dir"
    
    local backup_filename=$(generate_backup_filename "full")
    local backup_path="$BACKUP_DIR/$backup_filename"
    
    log_info "Compressing backup..."
    tar czf "$backup_path" -C "$temp_dir" .
    
    # Cleanup temp directory
    rm -rf "$temp_dir"
    
    log_success "Full backup created: $backup_filename"
    log_info "Backup size: $(du -h "$backup_path" | cut -f1)"
    
    echo "$backup_path"
}

# Create data-only backup
create_data_backup() {
    log_info "Creating data-only backup..."
    
    local temp_dir=$(backup_data)
    
    local backup_filename=$(generate_backup_filename "data")
    local backup_path="$BACKUP_DIR/$backup_filename"
    
    log_info "Compressing backup..."
    tar czf "$backup_path" -C "$temp_dir" .
    
    # Cleanup temp directory
    rm -rf "$temp_dir"
    
    log_success "Data backup created: $backup_filename"
    log_info "Backup size: $(du -h "$backup_path" | cut -f1)"
    
    echo "$backup_path"
}

# Create config-only backup
create_config_backup() {
    log_info "Creating configuration backup..."
    
    local temp_dir="$BACKUP_DIR/temp_$(date +%s)"
    mkdir -p "$temp_dir"
    
    backup_config "$temp_dir"
    
    local backup_filename=$(generate_backup_filename "config")
    local backup_path="$BACKUP_DIR/$backup_filename"
    
    log_info "Compressing backup..."
    tar czf "$backup_path" -C "$temp_dir" .
    
    # Cleanup temp directory
    rm -rf "$temp_dir"
    
    log_success "Configuration backup created: $backup_filename"
    log_info "Backup size: $(du -h "$backup_path" | cut -f1)"
    
    echo "$backup_path"
}

# List available backups
list_backups() {
    log_info "Available backups:"
    
    if [[ ! -d "$BACKUP_DIR" ]] || [[ -z "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]]; then
        log_warning "No backups found in $BACKUP_DIR"
        return
    fi
    
    echo "Backup files in $BACKUP_DIR:"
    echo "----------------------------------------"
    
    for backup in "$BACKUP_DIR"/expedition_planner_*.tar.gz; do
        if [[ -f "$backup" ]]; then
            local filename=$(basename "$backup")
            local size=$(du -h "$backup" | cut -f1)
            local date=$(stat -c %y "$backup" 2>/dev/null || stat -f %Sm "$backup" 2>/dev/null || echo "unknown")
            
            echo "$filename"
            echo "  Size: $size"
            echo "  Date: $date"
            echo ""
        fi
    done
}

# Restore from backup
restore_backup() {
    local backup_file="$1"
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    log_info "Restoring from backup: $(basename "$backup_file")"
    
    # Create temporary restore directory
    local restore_dir="$BACKUP_DIR/restore_$(date +%s)"
    mkdir -p "$restore_dir"
    
    # Extract backup
    log_info "Extracting backup..."
    tar xzf "$backup_file" -C "$restore_dir"
    
    # Check backup metadata
    if [[ -f "$restore_dir/backup_metadata.json" ]]; then
        log_info "Backup metadata:"
        cat "$restore_dir/backup_metadata.json"
    fi
    
    # Stop containers before restore
    if check_containers; then
        log_info "Stopping containers..."
        docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" down
    fi
    
    # Restore configuration files
    if [[ -d "$restore_dir/config" ]]; then
        log_info "Restoring configuration files..."
        cp -r "$restore_dir/config"/* "$PROJECT_ROOT/"
    fi
    
    # Restore data files
    if [[ -d "$restore_dir/data" ]]; then
        log_info "Restoring data files..."
        
        # Restore local directories
        if [[ -d "$restore_dir/data/uploads" ]]; then
            rm -rf "$PROJECT_ROOT/expedition_planner/uploads"
            cp -r "$restore_dir/data/uploads" "$PROJECT_ROOT/expedition_planner/"
        fi
        
        if [[ -d "$restore_dir/data/consolidated_outputs" ]]; then
            rm -rf "$PROJECT_ROOT/consolidated_outputs"
            cp -r "$restore_dir/data/consolidated_outputs" "$PROJECT_ROOT/"
        fi
    fi
    
    # Restore Docker volumes
    if [[ -d "$restore_dir/volumes" ]]; then
        log_info "Restoring Docker volumes..."
        
        # Start containers to create volumes
        docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" up -d
        sleep 10
        
        # Restore volume data
        for volume_backup in "$restore_dir/volumes"/*.tar.gz; do
            if [[ -f "$volume_backup" ]]; then
                local volume_name=$(basename "$volume_backup" .tar.gz)
                log_info "Restoring volume: $volume_name"
                
                docker run --rm \
                    -v "$volume_name":/target \
                    -v "$restore_dir/volumes":/backup:ro \
                    alpine sh -c "cd /target && tar xzf /backup/$(basename "$volume_backup")"
            fi
        done
    fi
    
    # Cleanup restore directory
    rm -rf "$restore_dir"
    
    log_success "Restore completed successfully"
    log_info "Please verify the application is working correctly"
}

# Cleanup old backups
cleanup_old_backups() {
    log_info "Cleaning up backups older than $RETENTION_DAYS days..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warning "Backup directory does not exist: $BACKUP_DIR"
        return
    fi
    
    local deleted_count=0
    
    find "$BACKUP_DIR" -name "expedition_planner_*.tar.gz" -type f -mtime +$RETENTION_DAYS | while read -r old_backup; do
        log_info "Removing old backup: $(basename "$old_backup")"
        rm "$old_backup"
        ((deleted_count++))
    done
    
    if [[ $deleted_count -eq 0 ]]; then
        log_info "No old backups to remove"
    else
        log_success "Removed $deleted_count old backup(s)"
    fi
}

# Main function
main() {
    local action="full"
    local restore_file=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --full)
                action="full"
                shift
                ;;
            --data-only)
                action="data"
                shift
                ;;
            --config-only)
                action="config"
                shift
                ;;
            --restore)
                action="restore"
                restore_file="$2"
                shift 2
                ;;
            --list)
                action="list"
                shift
                ;;
            --cleanup)
                action="cleanup"
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Create backup directory
    create_backup_dir
    
    # Execute action
    case $action in
        full)
            BACKUP_TYPE="full"
            create_full_backup
            ;;
        data)
            BACKUP_TYPE="data"
            create_data_backup
            ;;
        config)
            BACKUP_TYPE="config"
            create_config_backup
            ;;
        restore)
            restore_backup "$restore_file"
            ;;
        list)
            list_backups
            ;;
        cleanup)
            cleanup_old_backups
            ;;
    esac
}

# Run main function
main "$@"
