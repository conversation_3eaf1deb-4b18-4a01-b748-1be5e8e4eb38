#!/bin/bash

# Deployment Test Script for Expedition Planner
# Tests the deployment to ensure everything is working correctly

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_HOST="${TEST_HOST:-localhost}"
TEST_PORT="${TEST_PORT:-8080}"
OLLAMA_HOST="${OLLAMA_HOST:-localhost}"
OLLAMA_PORT="${OLLAMA_PORT:-11434}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test result functions
test_passed() {
    local test_name="$1"
    ((TESTS_PASSED++))
    log_success "✓ $test_name"
}

test_failed() {
    local test_name="$1"
    local error_msg="$2"
    ((TESTS_FAILED++))
    FAILED_TESTS+=("$test_name: $error_msg")
    log_error "✗ $test_name - $error_msg"
}

# Wait for service to be ready
wait_for_service() {
    local host="$1"
    local port="$2"
    local service_name="$3"
    local max_attempts="${4:-30}"
    
    log_info "Waiting for $service_name to be ready..."
    
    local attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s "http://$host:$port" >/dev/null 2>&1; then
            log_success "$service_name is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts - waiting for $service_name..."
        sleep 5
        ((attempt++))
    done
    
    log_error "$service_name is not ready after $max_attempts attempts"
    return 1
}

# Test Docker containers
test_containers() {
    log_info "Testing Docker containers..."
    
    # Check if containers are running
    local containers=("expedition-planner-app" "expedition-ollama")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            test_passed "Container $container is running"
        else
            test_failed "Container $container" "Container is not running"
        fi
    done
}

# Test application endpoints
test_application_endpoints() {
    log_info "Testing application endpoints..."
    
    # Test main page
    if curl -s -f "http://$TEST_HOST:$TEST_PORT/" >/dev/null; then
        test_passed "Main page accessible"
    else
        test_failed "Main page" "Not accessible"
    fi
    
    # Test configuration validation
    if curl -s -f "http://$TEST_HOST:$TEST_PORT/api/validate-configuration" >/dev/null; then
        test_passed "Configuration validation endpoint"
    else
        test_failed "Configuration validation" "Endpoint not accessible"
    fi
    
    # Test performance stats
    if curl -s -f "http://$TEST_HOST:$TEST_PORT/api/performance-stats" >/dev/null; then
        test_passed "Performance stats endpoint"
    else
        test_failed "Performance stats" "Endpoint not accessible"
    fi
    
    # Test storage status
    if curl -s -f "http://$TEST_HOST:$TEST_PORT/api/storage-status" >/dev/null; then
        test_passed "Storage status endpoint"
    else
        test_failed "Storage status" "Endpoint not accessible"
    fi
}

# Test Ollama service
test_ollama_service() {
    log_info "Testing Ollama service..."
    
    # Test Ollama API
    if curl -s -f "http://$OLLAMA_HOST:$OLLAMA_PORT/api/tags" >/dev/null; then
        test_passed "Ollama API accessible"
        
        # Check if required model is available
        local models=$(curl -s "http://$OLLAMA_HOST:$OLLAMA_PORT/api/tags" | jq -r '.models[].name' 2>/dev/null || echo "")
        if echo "$models" | grep -q "mistral"; then
            test_passed "Required model available"
        else
            test_failed "Required model" "Mistral model not found"
        fi
    else
        test_failed "Ollama API" "Not accessible"
    fi
}

# Test file upload functionality
test_file_upload() {
    log_info "Testing file upload functionality..."
    
    # Create a test file
    local test_file="/tmp/test_expedition.txt"
    cat > "$test_file" << EOF
Expedition Report - Test
Location: Test Island
Date: 2024-01-01
ETA: 08:00

Equipment:
- 4 Zodiacs
- 1 Twin

Groups:
Yellow Group - Departure: 08:00, Return: 10:00
Activity: Zodiac Cruise
EOF
    
    # Test file upload
    local response=$(curl -s -X POST "http://$TEST_HOST:$TEST_PORT/api/upload-documents" \
        -F "files=@$test_file" 2>/dev/null || echo "")
    
    if [[ -n "$response" ]] && echo "$response" | grep -q "session_id"; then
        test_passed "File upload functionality"
        
        # Extract session ID for further testing
        local session_id=$(echo "$response" | jq -r '.session_id' 2>/dev/null || echo "")
        if [[ -n "$session_id" && "$session_id" != "null" ]]; then
            test_passed "Session ID generation"
        else
            test_failed "Session ID" "Not generated properly"
        fi
    else
        test_failed "File upload" "Upload failed or invalid response"
    fi
    
    # Cleanup
    rm -f "$test_file"
}

# Test WebSocket connectivity
test_websocket() {
    log_info "Testing WebSocket connectivity..."
    
    # Simple WebSocket test using curl (limited)
    # In a real scenario, you'd use a WebSocket client
    local ws_response=$(curl -s -H "Connection: Upgrade" -H "Upgrade: websocket" \
        "http://$TEST_HOST:$TEST_PORT/socket.io/" 2>/dev/null || echo "")
    
    if [[ -n "$ws_response" ]]; then
        test_passed "WebSocket endpoint accessible"
    else
        test_failed "WebSocket" "Endpoint not accessible"
    fi
}

# Test circuit breakers
test_circuit_breakers() {
    log_info "Testing circuit breaker status..."
    
    local cb_response=$(curl -s "http://$TEST_HOST:$TEST_PORT/api/performance-stats" 2>/dev/null || echo "")
    
    if echo "$cb_response" | grep -q "circuit_breakers"; then
        test_passed "Circuit breaker monitoring"
    else
        test_failed "Circuit breakers" "Status not available"
    fi
}

# Test cleanup functionality
test_cleanup() {
    log_info "Testing cleanup functionality..."
    
    local cleanup_response=$(curl -s -X POST "http://$TEST_HOST:$TEST_PORT/api/cleanup-files" \
        -H "Content-Type: application/json" \
        -d '{"days_old": 30}' 2>/dev/null || echo "")
    
    if echo "$cleanup_response" | grep -q "success"; then
        test_passed "File cleanup functionality"
    else
        test_failed "File cleanup" "Cleanup failed"
    fi
}

# Test health checks
test_health_checks() {
    log_info "Testing health checks..."
    
    # Test application health
    local health_response=$(curl -s "http://$TEST_HOST:$TEST_PORT/api/validate-configuration" 2>/dev/null || echo "")
    
    if echo "$health_response" | grep -q "success"; then
        test_passed "Application health check"
    else
        test_failed "Health check" "Application not healthy"
    fi
}

# Performance test
test_performance() {
    log_info "Testing performance metrics..."
    
    local perf_response=$(curl -s "http://$TEST_HOST:$TEST_PORT/api/performance-stats" 2>/dev/null || echo "")
    
    if echo "$perf_response" | grep -q "memory"; then
        test_passed "Performance metrics collection"
        
        # Check memory usage
        local memory_usage=$(echo "$perf_response" | jq -r '.performance.memory.percent' 2>/dev/null || echo "0")
        if [[ $(echo "$memory_usage < 90" | bc -l 2>/dev/null || echo "1") -eq 1 ]]; then
            test_passed "Memory usage within limits"
        else
            test_failed "Memory usage" "Usage above 90%: $memory_usage%"
        fi
    else
        test_failed "Performance metrics" "Not available"
    fi
}

# Generate test report
generate_report() {
    echo ""
    echo "=========================================="
    echo "         DEPLOYMENT TEST REPORT"
    echo "=========================================="
    echo "Date: $(date)"
    echo "Host: $TEST_HOST:$TEST_PORT"
    echo "Ollama: $OLLAMA_HOST:$OLLAMA_PORT"
    echo ""
    echo "Results:"
    echo "  Tests Passed: $TESTS_PASSED"
    echo "  Tests Failed: $TESTS_FAILED"
    echo "  Total Tests:  $((TESTS_PASSED + TESTS_FAILED))"
    echo ""
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_success "🎉 All tests passed! Deployment is successful."
        echo ""
        echo "Your Expedition Planner is ready for use:"
        echo "  Application: http://$TEST_HOST:$TEST_PORT"
        echo "  Ollama API:  http://$OLLAMA_HOST:$OLLAMA_PORT"
        echo ""
        return 0
    else
        log_error "❌ Some tests failed. Please review the issues below:"
        echo ""
        for failed_test in "${FAILED_TESTS[@]}"; do
            echo "  - $failed_test"
        done
        echo ""
        echo "Check the logs for more details:"
        echo "  docker-compose logs expedition-planner"
        echo "  docker-compose logs ollama"
        echo ""
        return 1
    fi
}

# Main test function
main() {
    log_info "Starting Expedition Planner deployment tests..."
    log_info "Target: $TEST_HOST:$TEST_PORT"
    
    # Wait for services to be ready
    if ! wait_for_service "$TEST_HOST" "$TEST_PORT" "Expedition Planner" 30; then
        log_error "Expedition Planner is not accessible. Aborting tests."
        exit 1
    fi
    
    if ! wait_for_service "$OLLAMA_HOST" "$OLLAMA_PORT" "Ollama" 30; then
        log_warning "Ollama is not accessible. Some tests may fail."
    fi
    
    # Run all tests
    test_containers
    test_application_endpoints
    test_ollama_service
    test_file_upload
    test_websocket
    test_circuit_breakers
    test_cleanup
    test_health_checks
    test_performance
    
    # Generate and display report
    generate_report
}

# Help function
show_help() {
    cat << EOF
Deployment Test Script for Expedition Planner

Usage: $0 [options]

Options:
    --host HOST     Test host (default: localhost)
    --port PORT     Test port (default: 8080)
    --ollama-host   Ollama host (default: localhost)
    --ollama-port   Ollama port (default: 11434)
    --help          Show this help message

Examples:
    $0                                    # Test localhost deployment
    $0 --host myserver.com --port 80      # Test remote deployment
    $0 --ollama-host *************        # Test with remote Ollama

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            TEST_HOST="$2"
            shift 2
            ;;
        --port)
            TEST_PORT="$2"
            shift 2
            ;;
        --ollama-host)
            OLLAMA_HOST="$2"
            shift 2
            ;;
        --ollama-port)
            OLLAMA_PORT="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
