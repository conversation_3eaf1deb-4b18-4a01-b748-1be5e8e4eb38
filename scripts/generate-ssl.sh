#!/bin/bash

# SSL Certificate Generation Script for Expedition Planner
# Generates self-signed certificates for development/testing

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SSL_DIR="$PROJECT_ROOT/deployment/ssl"
DOMAIN="${1:-expedition-planner.local}"
DAYS_VALID="${2:-365}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
SSL Certificate Generation Script

Usage: $0 [domain] [days_valid]

Arguments:
    domain      Domain name for the certificate (default: expedition-planner.local)
    days_valid  Number of days the certificate is valid (default: 365)

Examples:
    $0                                    # Generate for expedition-planner.local
    $0 myapp.example.com                  # Generate for custom domain
    $0 myapp.example.com 730              # Generate for 2 years

Note: This generates self-signed certificates for development/testing.
For production, use certificates from a trusted CA like Let's Encrypt.

EOF
}

# Check dependencies
check_dependencies() {
    if ! command -v openssl >/dev/null 2>&1; then
        log_error "OpenSSL is not installed. Please install it and try again."
        exit 1
    fi
}

# Create SSL directory
create_ssl_directory() {
    log_info "Creating SSL directory..."
    mkdir -p "$SSL_DIR"
    mkdir -p "$SSL_DIR/private"
    chmod 700 "$SSL_DIR/private"
}

# Generate private key
generate_private_key() {
    log_info "Generating private key..."
    openssl genrsa -out "$SSL_DIR/private/expedition_planner.key" 2048
    chmod 600 "$SSL_DIR/private/expedition_planner.key"
    log_success "Private key generated"
}

# Generate certificate signing request
generate_csr() {
    log_info "Generating certificate signing request..."
    
    # Create OpenSSL config for CSR
    cat > "$SSL_DIR/csr.conf" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=US
ST=State
L=City
O=Organization
OU=IT Department
CN=$DOMAIN

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = localhost
DNS.3 = *.${DOMAIN}
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

    openssl req -new -key "$SSL_DIR/private/expedition_planner.key" \
        -out "$SSL_DIR/expedition_planner.csr" \
        -config "$SSL_DIR/csr.conf"
    
    log_success "Certificate signing request generated"
}

# Generate self-signed certificate
generate_certificate() {
    log_info "Generating self-signed certificate..."
    
    # Create certificate extensions file
    cat > "$SSL_DIR/cert.conf" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=US
ST=State
L=City
O=Organization
OU=IT Department
CN=$DOMAIN

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = localhost
DNS.3 = *.${DOMAIN}
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

    openssl x509 -req -in "$SSL_DIR/expedition_planner.csr" \
        -signkey "$SSL_DIR/private/expedition_planner.key" \
        -out "$SSL_DIR/expedition_planner.crt" \
        -days "$DAYS_VALID" \
        -extensions v3_req \
        -extfile "$SSL_DIR/cert.conf"
    
    log_success "Self-signed certificate generated"
}

# Generate DH parameters for enhanced security
generate_dhparam() {
    log_info "Generating DH parameters (this may take a while)..."
    openssl dhparam -out "$SSL_DIR/dhparam.pem" 2048
    log_success "DH parameters generated"
}

# Create certificate bundle
create_bundle() {
    log_info "Creating certificate bundle..."
    cat "$SSL_DIR/expedition_planner.crt" > "$SSL_DIR/expedition_planner_bundle.crt"
    log_success "Certificate bundle created"
}

# Verify certificate
verify_certificate() {
    log_info "Verifying certificate..."
    
    # Check certificate details
    log_info "Certificate details:"
    openssl x509 -in "$SSL_DIR/expedition_planner.crt" -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:|DNS:|IP Address:)"
    
    # Verify certificate against private key
    cert_modulus=$(openssl x509 -noout -modulus -in "$SSL_DIR/expedition_planner.crt" | openssl md5)
    key_modulus=$(openssl rsa -noout -modulus -in "$SSL_DIR/private/expedition_planner.key" | openssl md5)
    
    if [[ "$cert_modulus" == "$key_modulus" ]]; then
        log_success "Certificate and private key match"
    else
        log_error "Certificate and private key do not match"
        exit 1
    fi
}

# Set proper permissions
set_permissions() {
    log_info "Setting proper permissions..."
    
    # Certificate files (readable by all)
    chmod 644 "$SSL_DIR"/*.crt
    chmod 644 "$SSL_DIR"/*.pem
    
    # Private key (readable only by owner)
    chmod 600 "$SSL_DIR/private"/*.key
    
    # CSR and config files
    chmod 644 "$SSL_DIR"/*.csr
    chmod 644 "$SSL_DIR"/*.conf
    
    log_success "Permissions set"
}

# Cleanup temporary files
cleanup() {
    log_info "Cleaning up temporary files..."
    rm -f "$SSL_DIR"/*.csr
    rm -f "$SSL_DIR"/*.conf
    log_success "Cleanup completed"
}

# Generate installation instructions
generate_instructions() {
    log_info "Generating installation instructions..."
    
    cat > "$SSL_DIR/README.md" << EOF
# SSL Certificate Installation

## Files Generated

- \`expedition_planner.crt\` - SSL certificate
- \`private/expedition_planner.key\` - Private key
- \`expedition_planner_bundle.crt\` - Certificate bundle
- \`dhparam.pem\` - DH parameters

## Installation

### For Nginx (recommended)
Update your nginx.conf:

\`\`\`nginx
ssl_certificate /etc/nginx/ssl/expedition_planner.crt;
ssl_certificate_key /etc/nginx/ssl/expedition_planner.key;
ssl_dhparam /etc/nginx/ssl/dhparam.pem;
\`\`\`

### For Apache
Update your virtual host:

\`\`\`apache
SSLCertificateFile /etc/ssl/certs/expedition_planner.crt
SSLCertificateKeyFile /etc/ssl/private/expedition_planner.key
\`\`\`

## Security Notes

⚠️  **IMPORTANT**: These are self-signed certificates for development/testing only.

For production:
1. Use certificates from a trusted CA (Let's Encrypt, etc.)
2. Keep private keys secure and never commit them to version control
3. Regularly rotate certificates before expiration

## Certificate Details

- Domain: $DOMAIN
- Valid for: $DAYS_VALID days
- Generated: $(date)
- Expires: $(date -d "+$DAYS_VALID days" 2>/dev/null || date -v +${DAYS_VALID}d 2>/dev/null || echo "Check certificate for expiration date")

## Browser Trust

To trust this certificate in your browser:
1. Navigate to https://$DOMAIN
2. Click "Advanced" when you see the security warning
3. Click "Proceed to $DOMAIN (unsafe)"

Or add the certificate to your system's trusted certificates.

EOF

    log_success "Installation instructions created at $SSL_DIR/README.md"
}

# Main function
main() {
    # Parse arguments
    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
    esac
    
    log_info "Generating SSL certificates for Expedition Planner"
    log_info "Domain: $DOMAIN"
    log_info "Valid for: $DAYS_VALID days"
    log_info "SSL directory: $SSL_DIR"
    
    check_dependencies
    create_ssl_directory
    generate_private_key
    generate_csr
    generate_certificate
    generate_dhparam
    create_bundle
    verify_certificate
    set_permissions
    cleanup
    generate_instructions
    
    log_success "SSL certificate generation completed!"
    log_info "Certificate files are located in: $SSL_DIR"
    log_info "Read $SSL_DIR/README.md for installation instructions"
    
    log_warning "Remember: These are self-signed certificates for development only"
    log_warning "Use trusted CA certificates for production deployments"
}

# Run main function
main "$@"
