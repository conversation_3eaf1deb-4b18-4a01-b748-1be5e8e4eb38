#!/bin/bash

# Ollama Setup Script for Expedition Planner
# This script sets up Ollama with the required models

set -euo pipefail

# Configuration
OLLAMA_HOST="${OLLAMA_HOST:-http://localhost:11434}"
REQUIRED_MODEL="${OLLAMA_MODEL:-mistral-7b-v0-1-gguf:latest}"
FALLBACK_MODEL="mistral:latest"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Ollama is running
check_ollama_status() {
    log_info "Checking Ollama service status..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s "$OLLAMA_HOST/api/tags" >/dev/null 2>&1; then
            log_success "Ollama service is running"
            return 0
        else
            log_info "Waiting for Ollama service... (attempt $attempt/$max_attempts)"
            sleep 5
            ((attempt++))
        fi
    done
    
    log_error "Ollama service is not responding after $max_attempts attempts"
    return 1
}

# List available models
list_models() {
    log_info "Available models:"
    curl -s "$OLLAMA_HOST/api/tags" | jq -r '.models[].name' 2>/dev/null || {
        log_warning "Could not parse model list"
        curl -s "$OLLAMA_HOST/api/tags"
    }
}

# Check if a model exists
model_exists() {
    local model_name="$1"
    curl -s "$OLLAMA_HOST/api/tags" | jq -r '.models[].name' | grep -q "^$model_name$"
}

# Pull a model
pull_model() {
    local model_name="$1"
    log_info "Pulling model: $model_name"
    
    # Start the pull request
    local pull_response=$(curl -s -X POST "$OLLAMA_HOST/api/pull" \
        -H "Content-Type: application/json" \
        -d "{\"name\": \"$model_name\"}")
    
    # Monitor the pull progress
    log_info "Downloading model... This may take several minutes."
    
    # Simple progress monitoring
    local max_wait=1800  # 30 minutes
    local elapsed=0
    local check_interval=10
    
    while [[ $elapsed -lt $max_wait ]]; do
        if model_exists "$model_name"; then
            log_success "Model $model_name downloaded successfully"
            return 0
        fi
        
        log_info "Still downloading... (${elapsed}s elapsed)"
        sleep $check_interval
        elapsed=$((elapsed + check_interval))
    done
    
    log_error "Model download timed out after ${max_wait}s"
    return 1
}

# Test model functionality
test_model() {
    local model_name="$1"
    log_info "Testing model: $model_name"
    
    local test_prompt="Hello, please respond with 'Model test successful'"
    local response=$(curl -s -X POST "$OLLAMA_HOST/api/generate" \
        -H "Content-Type: application/json" \
        -d "{
            \"model\": \"$model_name\",
            \"prompt\": \"$test_prompt\",
            \"stream\": false,
            \"options\": {
                \"temperature\": 0.1,
                \"num_predict\": 20
            }
        }")
    
    if echo "$response" | jq -r '.response' | grep -q "successful\|test\|hello"; then
        log_success "Model $model_name is working correctly"
        return 0
    else
        log_warning "Model test may have failed. Response: $(echo "$response" | jq -r '.response' 2>/dev/null || echo "$response")"
        return 1
    fi
}

# Setup required model
setup_model() {
    local model_name="$1"
    
    if model_exists "$model_name"; then
        log_success "Model $model_name is already available"
        test_model "$model_name"
        return 0
    else
        log_info "Model $model_name not found, attempting to pull..."
        if pull_model "$model_name"; then
            test_model "$model_name"
            return 0
        else
            return 1
        fi
    fi
}

# Main setup function
main() {
    log_info "Setting up Ollama for Expedition Planner..."
    log_info "Ollama Host: $OLLAMA_HOST"
    log_info "Required Model: $REQUIRED_MODEL"
    
    # Check if Ollama is running
    if ! check_ollama_status; then
        log_error "Please ensure Ollama is running and accessible at $OLLAMA_HOST"
        log_info "To start Ollama:"
        log_info "  - Docker: docker run -d -p 11434:11434 ollama/ollama"
        log_info "  - Local: ollama serve"
        exit 1
    fi
    
    # List current models
    log_info "Current models:"
    list_models
    
    # Setup required model
    if setup_model "$REQUIRED_MODEL"; then
        log_success "Required model $REQUIRED_MODEL is ready"
    else
        log_warning "Failed to setup required model $REQUIRED_MODEL"
        log_info "Trying fallback model $FALLBACK_MODEL"
        
        if setup_model "$FALLBACK_MODEL"; then
            log_success "Fallback model $FALLBACK_MODEL is ready"
            log_warning "Update your configuration to use $FALLBACK_MODEL instead of $REQUIRED_MODEL"
        else
            log_error "Failed to setup any suitable model"
            log_info "Available models for manual installation:"
            log_info "  - mistral:latest (recommended)"
            log_info "  - llama2:latest"
            log_info "  - codellama:latest"
            log_info ""
            log_info "To manually install a model:"
            log_info "  ollama pull mistral:latest"
            exit 1
        fi
    fi
    
    # Final verification
    log_info "Final model verification:"
    list_models
    
    log_success "Ollama setup completed successfully!"
    log_info "You can now start the Expedition Planner application"
}

# Help function
show_help() {
    cat << EOF
Ollama Setup Script for Expedition Planner

Usage: $0 [options]

Options:
    --host URL      Set Ollama host URL (default: http://localhost:11434)
    --model NAME    Set required model name (default: mistral-7b-v0-1-gguf:latest)
    --list          List available models and exit
    --test MODEL    Test a specific model and exit
    --help          Show this help message

Environment Variables:
    OLLAMA_HOST     Ollama service URL
    OLLAMA_MODEL    Required model name

Examples:
    $0                                  # Setup with defaults
    $0 --host http://*************:11434
    $0 --model mistral:latest
    $0 --list
    $0 --test mistral:latest

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            OLLAMA_HOST="$2"
            shift 2
            ;;
        --model)
            REQUIRED_MODEL="$2"
            shift 2
            ;;
        --list)
            check_ollama_status && list_models
            exit 0
            ;;
        --test)
            check_ollama_status && test_model "$2"
            exit $?
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
