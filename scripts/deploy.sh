#!/bin/bash

# Expedition Planner Deployment Script
# Usage: ./scripts/deploy.sh [environment] [options]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Expedition Planner Deployment Script

Usage: $0 [environment] [options]

Environments:
    development     Deploy for development (default ports, debug enabled)
    staging         Deploy for staging environment
    production      Deploy for production (default)

Options:
    --build         Force rebuild of Docker images
    --pull          Pull latest images before deployment
    --logs          Show logs after deployment
    --health        Run health checks after deployment
    --backup        Create backup before deployment
    --rollback      Rollback to previous version
    --help          Show this help message

Examples:
    $0 production --build --health
    $0 development --logs
    $0 staging --backup --pull

EOF
}

# Parse command line arguments
BUILD_FLAG=""
PULL_FLAG=""
SHOW_LOGS=false
RUN_HEALTH_CHECK=false
CREATE_BACKUP=false
ROLLBACK=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --build)
            BUILD_FLAG="--build"
            shift
            ;;
        --pull)
            PULL_FLAG="--pull"
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        --health)
            RUN_HEALTH_CHECK=true
            shift
            ;;
        --backup)
            CREATE_BACKUP=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            if [[ -z "${ENVIRONMENT:-}" ]]; then
                ENVIRONMENT="$1"
            fi
            shift
            ;;
    esac
done

# Validate environment
case $ENVIRONMENT in
    development|staging|production)
        log_info "Deploying to $ENVIRONMENT environment"
        ;;
    *)
        log_error "Invalid environment: $ENVIRONMENT"
        log_info "Valid environments: development, staging, production"
        exit 1
        ;;
esac

# Set environment-specific configurations
setup_environment() {
    case $ENVIRONMENT in
        development)
            ENV_FILE=".env.development"
            COMPOSE_FILE="docker-compose.yml"
            ;;
        staging)
            ENV_FILE=".env.staging"
            COMPOSE_FILE="docker-compose.staging.yml"
            ;;
        production)
            ENV_FILE=".env.production"
            COMPOSE_FILE="docker-compose.yml"
            ;;
    esac

    # Create environment file if it doesn't exist
    if [[ ! -f "$PROJECT_ROOT/$ENV_FILE" ]]; then
        log_warning "Environment file $ENV_FILE not found, copying from .env.example"
        cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/$ENV_FILE"
        log_warning "Please edit $ENV_FILE with your configuration before deploying"
        exit 1
    fi
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi

    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "docker-compose is not installed. Please install it and try again."
        exit 1
    fi

    # Check if required files exist
    local required_files=("$COMPOSE_FILE" "$ENV_FILE" "Dockerfile")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$PROJECT_ROOT/$file" ]]; then
            log_error "Required file not found: $file"
            exit 1
        fi
    done

    # Check if Ollama model is available (for production)
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "Checking Ollama model availability..."
        # This will be checked during health checks
    fi

    log_success "Pre-deployment checks passed"
}

# Create backup
create_backup() {
    if [[ "$CREATE_BACKUP" == true ]]; then
        log_info "Creating backup..."
        
        local backup_dir="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        # Backup volumes
        docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" --env-file "$PROJECT_ROOT/$ENV_FILE" \
            exec -T expedition-planner tar czf - /app/expedition_planner/uploads /app/consolidated_outputs \
            > "$backup_dir/data_backup.tar.gz" 2>/dev/null || true
        
        # Backup configuration
        cp "$PROJECT_ROOT/$ENV_FILE" "$backup_dir/"
        cp "$PROJECT_ROOT/$COMPOSE_FILE" "$backup_dir/"
        
        log_success "Backup created at $backup_dir"
    fi
}

# Deploy application
deploy_application() {
    log_info "Deploying Expedition Planner..."

    cd "$PROJECT_ROOT"

    # Pull images if requested
    if [[ -n "$PULL_FLAG" ]]; then
        log_info "Pulling latest images..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull
    fi

    # Stop existing containers
    log_info "Stopping existing containers..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down

    # Start services
    log_info "Starting services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d $BUILD_FLAG

    log_success "Deployment completed"
}

# Health checks
run_health_checks() {
    if [[ "$RUN_HEALTH_CHECK" == true ]]; then
        log_info "Running health checks..."

        # Wait for services to start
        sleep 30

        # Check if containers are running
        local containers=("expedition-planner-app" "expedition-ollama")
        for container in "${containers[@]}"; do
            if docker ps --format "table {{.Names}}" | grep -q "$container"; then
                log_success "$container is running"
            else
                log_error "$container is not running"
                return 1
            fi
        done

        # Check application health endpoint
        local max_attempts=10
        local attempt=1
        while [[ $attempt -le $max_attempts ]]; do
            if curl -f http://localhost:8080/api/validate-configuration >/dev/null 2>&1; then
                log_success "Application health check passed"
                break
            else
                log_warning "Health check attempt $attempt/$max_attempts failed, retrying..."
                sleep 10
                ((attempt++))
            fi
        done

        if [[ $attempt -gt $max_attempts ]]; then
            log_error "Application health check failed after $max_attempts attempts"
            return 1
        fi

        # Check Ollama service
        if curl -f http://localhost:11434/api/tags >/dev/null 2>&1; then
            log_success "Ollama service is healthy"
        else
            log_warning "Ollama service health check failed"
        fi

        log_success "All health checks passed"
    fi
}

# Show logs
show_logs() {
    if [[ "$SHOW_LOGS" == true ]]; then
        log_info "Showing application logs..."
        docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" --env-file "$PROJECT_ROOT/$ENV_FILE" \
            logs -f expedition-planner
    fi
}

# Rollback function
rollback_deployment() {
    if [[ "$ROLLBACK" == true ]]; then
        log_info "Rolling back deployment..."
        
        # Find latest backup
        local latest_backup=$(find "$PROJECT_ROOT/backups" -type d -name "*_*" | sort -r | head -n1)
        
        if [[ -n "$latest_backup" ]]; then
            log_info "Rolling back to backup: $latest_backup"
            
            # Stop current deployment
            docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" --env-file "$PROJECT_ROOT/$ENV_FILE" down
            
            # Restore configuration
            cp "$latest_backup/$ENV_FILE" "$PROJECT_ROOT/"
            cp "$latest_backup/$COMPOSE_FILE" "$PROJECT_ROOT/"
            
            # Restore data
            if [[ -f "$latest_backup/data_backup.tar.gz" ]]; then
                docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" --env-file "$PROJECT_ROOT/$ENV_FILE" \
                    run --rm expedition-planner tar xzf - -C / < "$latest_backup/data_backup.tar.gz"
            fi
            
            # Restart services
            docker-compose -f "$PROJECT_ROOT/$COMPOSE_FILE" --env-file "$PROJECT_ROOT/$ENV_FILE" up -d
            
            log_success "Rollback completed"
        else
            log_error "No backup found for rollback"
            exit 1
        fi
        
        exit 0
    fi
}

# Main deployment flow
main() {
    log_info "Starting Expedition Planner deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Compose file: $COMPOSE_FILE"
    log_info "Environment file: $ENV_FILE"

    setup_environment
    rollback_deployment  # Check if rollback is requested
    pre_deployment_checks
    create_backup
    deploy_application
    run_health_checks
    show_logs

    log_success "Deployment completed successfully!"
    log_info "Application is available at: http://localhost:8080"
    log_info "Ollama service is available at: http://localhost:11434"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "Production deployment notes:"
        log_info "- Configure SSL certificates in deployment/ssl/"
        log_info "- Update DNS records to point to your server"
        log_info "- Set up monitoring and alerting"
        log_info "- Configure automated backups"
    fi
}

# Run main function
main "$@"
