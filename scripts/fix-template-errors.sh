#!/bin/bash

# Fix template errors and restart development server
# This script fixes Jinja2 template issues and clears caches

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "🔧 Fixing template errors and restarting development server..."

# Check if we're in the right directory
if [[ ! -f "expedition_planner/web/templates/pattern_analysis.html" ]]; then
    log_error "Please run this script from the project root directory"
    exit 1
fi

# Fix the pattern_analysis.html template if it still has issues
log_info "Checking pattern_analysis.html template..."

# Check if the file ends with {% endblock %}
if tail -1 expedition_planner/web/templates/pattern_analysis.html | grep -q "{% endblock %}"; then
    log_warning "Found incorrect {% endblock %} in pattern_analysis.html, fixing..."
    
    # Remove the last line and add proper HTML closing tags
    head -n -1 expedition_planner/web/templates/pattern_analysis.html > /tmp/pattern_analysis_fixed.html
    echo "</body>" >> /tmp/pattern_analysis_fixed.html
    echo "</html>" >> /tmp/pattern_analysis_fixed.html
    
    # Replace the original file
    mv /tmp/pattern_analysis_fixed.html expedition_planner/web/templates/pattern_analysis.html
    log_success "Fixed pattern_analysis.html template"
else
    log_success "pattern_analysis.html template is already correct"
fi

# Clear Python cache
log_info "Clearing Python cache..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# Clear Flask template cache by restarting the application
log_info "Restarting development server to clear template cache..."

# If running with Docker
if docker-compose ps | grep -q "expedition-planner"; then
    log_info "Restarting Docker containers..."
    docker-compose restart expedition-planner
    
    # Wait for the service to be ready
    log_info "Waiting for application to restart..."
    sleep 10
    
    # Check if the application is responding
    for i in {1..30}; do
        if curl -s -f http://localhost:8080/ >/dev/null 2>&1; then
            log_success "Application is responding"
            break
        fi
        if [[ $i -eq 30 ]]; then
            log_error "Application is not responding after restart"
            exit 1
        fi
        sleep 2
    done
    
# If running natively
elif pgrep -f "expedition_planner.web.langchain_app" >/dev/null; then
    log_info "Stopping native Python process..."
    pkill -f "expedition_planner.web.langchain_app" || true
    sleep 2
    
    log_info "Starting application in background..."
    cd "$(dirname "$0")/.."
    python -m expedition_planner.web.langchain_app &
    
    # Wait for the service to be ready
    log_info "Waiting for application to start..."
    sleep 5
    
    # Check if the application is responding
    for i in {1..30}; do
        if curl -s -f http://localhost:8080/ >/dev/null 2>&1; then
            log_success "Application is responding"
            break
        fi
        if [[ $i -eq 30 ]]; then
            log_error "Application is not responding after restart"
            exit 1
        fi
        sleep 2
    done
else
    log_warning "No running application found. Please start it manually:"
    echo "  Docker: docker-compose up -d"
    echo "  Native: python -m expedition_planner.web.langchain_app"
fi

# Test the fixed endpoints
log_info "Testing application endpoints..."

# Test main page
if curl -s -f http://localhost:8080/ >/dev/null; then
    log_success "✅ Main page is working"
else
    log_error "❌ Main page is not responding"
fi

# Test agent interface
if curl -s -f http://localhost:8080/agent-interface >/dev/null; then
    log_success "✅ Agent interface is working"
else
    log_error "❌ Agent interface is not responding"
fi

# Test pattern analysis (this was the failing one)
if curl -s -f http://localhost:8080/pattern-analysis >/dev/null; then
    log_success "✅ Pattern analysis page is working"
else
    log_error "❌ Pattern analysis page is still failing"
    log_info "Check logs: docker-compose logs expedition-planner"
fi

# Test API endpoints
if curl -s -f http://localhost:8080/api/validate-configuration >/dev/null; then
    log_success "✅ API is working"
else
    log_error "❌ API is not responding"
fi

log_success "🎉 Template fixes completed!"
echo ""
echo "📊 Application Status:"
echo "  - Main Page: http://localhost:8080"
echo "  - Agent Interface: http://localhost:8080/agent-interface"
echo "  - Pattern Analysis: http://localhost:8080/pattern-analysis"
echo "  - API Health: http://localhost:8080/api/validate-configuration"
echo ""
echo "📝 If you still see issues:"
echo "  1. Clear your browser cache (Ctrl+F5 or Cmd+Shift+R)"
echo "  2. Check logs: docker-compose logs -f expedition-planner"
echo "  3. Restart completely: docker-compose down && docker-compose up -d"
echo ""
echo "🔧 For JavaScript 404 errors:"
echo "  - These are usually browser cache issues"
echo "  - Try opening in an incognito/private window"
echo "  - The application should still work despite these warnings"
