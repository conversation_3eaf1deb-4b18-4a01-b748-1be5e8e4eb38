#!/bin/bash

# Quick fix script for development memory issues
# This script updates configuration and restarts the development environment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "🔧 Fixing development memory issues..."

# Check if .env.development exists
if [[ ! -f ".env.development" ]]; then
    log_info "Creating .env.development from template..."
    cp .env.example .env.development
fi

# Update memory settings in .env.development
log_info "Updating memory settings in .env.development..."

# Create a temporary file with updated settings
cat > /tmp/memory_settings << 'EOF'
# Development mode settings
DEV_MODE=true
WEB_DEBUG=true
LOG_LEVEL=DEBUG

# Increased memory limits for development
MAX_MEMORY_MB=2000.0
MAX_MEMORY_PERCENT=95.0

# Reduced workers to save memory
MAX_WORKERS=2
PARALLEL_CHUNK_SIZE=1

# Larger file limits for development
MAX_FILE_SIZE_MB=100
MAX_STORAGE_MB=500

# Faster cleanup
CLEANUP_DAYS=1

# Relaxed circuit breakers
LLM_FAILURE_THRESHOLD=10
LLM_RECOVERY_TIMEOUT=10
FILE_FAILURE_THRESHOLD=10
FILE_RECOVERY_TIMEOUT=10
EOF

# Update the .env.development file
log_info "Applying memory configuration updates..."

# Function to update or add environment variable
update_env_var() {
    local var_name="$1"
    local var_value="$2"
    local env_file=".env.development"
    
    if grep -q "^${var_name}=" "$env_file"; then
        # Variable exists, update it
        sed -i.bak "s/^${var_name}=.*/${var_name}=${var_value}/" "$env_file"
    else
        # Variable doesn't exist, add it
        echo "${var_name}=${var_value}" >> "$env_file"
    fi
}

# Apply all the memory-related settings
update_env_var "DEV_MODE" "true"
update_env_var "WEB_DEBUG" "true"
update_env_var "LOG_LEVEL" "DEBUG"
update_env_var "MAX_MEMORY_MB" "2000.0"
update_env_var "MAX_MEMORY_PERCENT" "95.0"
update_env_var "MAX_WORKERS" "2"
update_env_var "PARALLEL_CHUNK_SIZE" "1"
update_env_var "MAX_FILE_SIZE_MB" "100"
update_env_var "MAX_STORAGE_MB" "500"
update_env_var "CLEANUP_DAYS" "1"
update_env_var "LLM_FAILURE_THRESHOLD" "10"
update_env_var "LLM_RECOVERY_TIMEOUT" "10"
update_env_var "FILE_FAILURE_THRESHOLD" "10"
update_env_var "FILE_RECOVERY_TIMEOUT" "10"

log_success "Memory configuration updated!"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    log_error "Docker is not running. Please start Docker and run this script again."
    exit 1
fi

# Stop current containers
log_info "Stopping current containers..."
docker-compose down 2>/dev/null || true

# Clean up any orphaned containers
log_info "Cleaning up..."
docker-compose rm -f 2>/dev/null || true

# Start with the new configuration
log_info "Starting development environment with updated memory settings..."
docker-compose --env-file .env.development up -d --build

# Wait a moment for services to start
log_info "Waiting for services to start..."
sleep 10

# Check if services are running
log_info "Checking service health..."

# Check application
if curl -s -f http://localhost:8080/api/validate-configuration >/dev/null 2>&1; then
    log_success "✅ Application is running and healthy"
else
    log_warning "⚠️  Application may still be starting up..."
fi

# Check Ollama
if curl -s -f http://localhost:11434/api/tags >/dev/null 2>&1; then
    log_success "✅ Ollama service is running"
else
    log_warning "⚠️  Ollama may still be starting up..."
fi

log_success "🎉 Development environment restarted with increased memory limits!"
echo ""
echo "📊 New Memory Settings:"
echo "  - Max Memory: 2000 MB (was 500 MB)"
echo "  - Memory Threshold: 95% (was 80%)"
echo "  - Workers: 2 (was 4)"
echo "  - Chunk Size: 1 (was 2)"
echo ""
echo "🌐 Access your application:"
echo "  - Web Interface: http://localhost:8080"
echo "  - API Health: http://localhost:8080/api/validate-configuration"
echo "  - Performance Stats: http://localhost:8080/api/performance-stats"
echo ""
echo "📝 View logs:"
echo "  docker-compose logs -f expedition-planner"
echo ""
echo "🔧 If you still get memory errors:"
echo "  1. Increase Docker Desktop memory limit to 8GB+"
echo "  2. Close other memory-intensive applications"
echo "  3. Try processing smaller files first"

# Cleanup
rm -f /tmp/memory_settings
rm -f .env.development.bak 2>/dev/null || true
